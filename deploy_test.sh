#!/bin/bash

# Fullstack deployment script for test environment: Backend and Frontend to Cloud Run
# Run from project root (/home/<USER>/Documents/cqe-wallmart)
# Usage: ./deploy_test.sh
# Deploys to test services 'backend-test-wallmart' and 'frontend-test-wallmart'

set -e  # Exit on error

BACKEND_DIR="backend"
FRONTEND_DIR="frontend"
SERVICE_NAME="backend-test-wallmart"
FRONTEND_SERVICE_NAME="frontend-test-wallmart"
PROJECT_ID="ops-excellence"
LOCATION="us-central1"
REPOSITORY="walmart-docker"
IMAGE_NAME="project_walmart"
FRONTEND_IMAGE_NAME="project_walmart_frontend"
TAG="test-latest"

echo "🚀 Starting fullstack test deployment..."

# Step 1: Deploy Backend (Test)
echo "=== Backend Test Deployment ==="
cd ${BACKEND_DIR}

# Authenticate Docker
echo "🔐 Authenticating Docker..."
gcloud auth configure-docker ${LOCATION}-docker.pkg.dev

# Build image
echo "🏗️ Building backend test image..."
docker build -t ${IMAGE_NAME}:${TAG} .

# Tag for Artifact Registry
FULL_IMAGE_NAME="${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${IMAGE_NAME}:${TAG}"
docker tag ${IMAGE_NAME}:${TAG} ${FULL_IMAGE_NAME}

# Push to Artifact Registry
echo "📤 Pushing backend test to Artifact Registry..."
docker push ${FULL_IMAGE_NAME}

# Deploy to Cloud Run (Test Service)
echo "☁️ Deploying backend test to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
  --image ${FULL_IMAGE_NAME} \
  --platform managed \
  --region ${LOCATION} \
  --allow-unauthenticated \
  --project ${PROJECT_ID} \
  --port 8080  # Explicit port for Cloud Run

if [ $? -ne 0 ]; then
  echo "❌ Backend test deployment failed."
  exit 1
fi

# Get backend test URL
BACKEND_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${LOCATION} --format='value(status.url)' --project=${PROJECT_ID})
echo "✅ Backend test deployed at: ${BACKEND_URL}"

cd ..

# Step 2: Deploy Frontend (Test)
echo "=== Frontend Test Deployment ==="
cd ${FRONTEND_DIR}

# Authenticate Docker
echo "🔐 Authenticating Docker..."
gcloud auth configure-docker ${LOCATION}-docker.pkg.dev

# Build image with backend URL
echo "🏗️ Building frontend test image..."
docker build --no-cache --build-arg VITE_API_BASE_URL=${BACKEND_URL} -t ${FRONTEND_IMAGE_NAME}:${TAG} .

# Tag for Artifact Registry
FULL_IMAGE_NAME="${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${FRONTEND_IMAGE_NAME}:${TAG}"
docker tag ${FRONTEND_IMAGE_NAME}:${TAG} ${FULL_IMAGE_NAME}

# Push to Artifact Registry
echo "📤 Pushing frontend test to Artifact Registry..."
docker push ${FULL_IMAGE_NAME}

# Deploy to Cloud Run (Test Service)
echo "☁️ Deploying frontend test to Cloud Run..."
gcloud run deploy ${FRONTEND_SERVICE_NAME} \
  --image ${FULL_IMAGE_NAME} \
  --platform managed \
  --region ${LOCATION} \
  --allow-unauthenticated \
  --project ${PROJECT_ID} \
  --port 80  # nginx default port

if [ $? -ne 0 ]; then
  echo "❌ Frontend test deployment failed."
  exit 1
fi

# Get frontend test URL
FRONTEND_URL=$(gcloud run services describe ${FRONTEND_SERVICE_NAME} --region=${LOCATION} --format='value(status.url)' --project=${PROJECT_ID})
echo "✅ Frontend test deployed at: ${FRONTEND_URL}"

cd ..

echo "🎉 Fullstack test deployment complete!"
echo "Backend Test URL: ${BACKEND_URL}"
echo "Frontend Test URL: ${FRONTEND_URL}"