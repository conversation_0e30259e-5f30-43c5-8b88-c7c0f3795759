from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Form
from fastapi.responses import J<PERSON>NResponse
from typing import List

app = FastAPI()

@app.post("/upload")
async def upload(file: UploadFile = File(...)):
    # Process file and return columns and preview
    return JSONResponse(content={"columns": [], "preview": []})

@app.post("/query")
async def query(prompt: str = Form(...), model: str = Form(...), use_grounding: bool = Form(False), context: str = Form('')):
    # Stream response
    return JSONResponse(content="Streaming response")