from fastapi import APIRouter, UploadFile, File, Form
from fastapi.responses import JSONResponse

router = APIRouter()

@router.post("/upload")
async def upload(file: UploadFile = File(...)):
    # Process file and return columns and preview
    return JSONResponse(content={"columns": [], "preview": []})

@router.post("/query")
async def query(prompt: str = Form(...), model: str = Form(...), use_grounding: bool = Form(False), context: str = Form('')):
    # Stream response
    return JSONResponse(content="Streaming response")