import os
import json
from dotenv import load_dotenv
from google.oauth2 import service_account
from google.cloud import storage, bigquery

# ===============================
# Load .env file
# ===============================
load_dotenv()  # <-- this actually loads your .env vars into os.environ

# ===============================
# Google Cloud Config
# ===============================
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
SERVICE_ACCOUNT_FILE = os.path.join(BASE_DIR, "ops-excellence.json")

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE
)

PROJECT_ID = credentials.project_id or os.getenv("GCP_PROJECT_ID", "ops-excellence")
DATASET_ID = "walmart"

# ===============================
# Tables & Buckets
# ===============================
ASSIGN_TABLE = "details"
USERS_TABLE = "users"
BUCKET_NAME = "wal_test_bucket"
THREAD_FOLDER = "original4"

ASSIGN_TABLE_FULL = f"{PROJECT_ID}.{DATASET_ID}.{ASSIGN_TABLE}"
USERS_TABLE_FULL = f"{PROJECT_ID}.{DATASET_ID}.{USERS_TABLE}"
# ===============================
# API Keys
# ===============================
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
CRAWLBASE_API_KEY = os.getenv("CRAWLBASE_API_KEY")
SERPER_API_KEY = os.getenv("SERPER_API_KEY")

_missing = [k for k, v in {
    "GEMINI_API_KEY": GEMINI_API_KEY,
    "CRAWLBASE_API_KEY": CRAWLBASE_API_KEY,
    "SERPER_API_KEY": SERPER_API_KEY
}.items() if not v]

if _missing:
    raise RuntimeError(f"Missing required env vars: {', '.join(_missing)}")

# ===============================
# GCP Clients
# ===============================
def get_gcs_client():
    return storage.Client(credentials=credentials, project=PROJECT_ID)

def get_bq_client():
    return bigquery.Client(credentials=credentials, project=PROJECT_ID)

# ===============================
# Constants
# ===============================
DEFAULT_DOMAIN = "Vehicle"
SCHEMA_PATH = "routes/output.json"

HTTP_TIMEOUT = 30
CONCURRENT_LIMIT = 5
PLAIN_TEXT_SLICE = 1600
MAX_COMPETITORS = 3
