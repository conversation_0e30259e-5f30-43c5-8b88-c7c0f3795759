[{'attribute_name': 'Product Name', 'display_name': 'Product Name', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'Be sure to validate this after you curate and validate all other attributes required to create the Product Name.  The attributes are different from each PTG and are listed in the concatenating values. You will then concatenate this value. ;;1. Please make sure that the item’s Product Name value is in line with the appropriate designated Product Type name structure in the formatting section below. All mandatory attributes listed in the formatting section have to be concatenated to generate the Product Name. If any of them are blank then fail the attribute and follow-up with the supplier communication.  If the value does not meet this requirement, please mark the Product Name as an error. Make sure when the Product Name is fixed post supplier communication, we will fix the Product Name in the dependent attributes like Long Description. If the Product Name seems to be similar do not edit the Product Name within the Short Description. ;2. If Product Name is a part of Short Description and if any compliance-related attributes like certification are being edited as a part of Product Name then that should reflect on all dependent attributes including Short and Long Description and other compliant attributes.;;3. Remove promotional text like free shipping or discounts from Product Name. ; - Example:  20% off!;- Example: Free shipping!;4. Remove text that promotes another retailer.  It should not have any mention of another retailer unless it is related to the product. Any Blacklisted/Superlative/Competitive words to be removed as a part of the Product Name even if it is part of our concatenation logic.;5.  Any claim or certification listed in the product name must be validated using the product images provided by the supplier;6. Remove duplicate words unless they are duplicated for an obvious reason without breaking the free-flow (Makes grammatically sense) of the Product Name.;7. Please leave trademarked or copyright symbols. Please remove symbols like €,…,†,‡,•,¢,£,¥,±,¶,~,â, ???;8. No ALL CAPS or all lower case in Product Name. Product Name should be in proper casing unless all caps or lower casing is applicable. Brands that have been validated in Walle that are in ALL CAPS or all lower case are acceptable. Trademarked or copyright information that is in ALL CAPS or all lower case also acceptable.', 'definition': 'Title of the product to be displayed on the Item Page. Your title should ideally be under 90 Characters, Maximum 100 Characters.  Anything over 90 characters will negatively impact your Search Engine Optimization (SEO).', 'examples': 'DGP Parts P/N 31062 2-1/2 in Inlet/Outlet 18-3/16 in Exhaust Silencer, 2 Pieces', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 10, 'max_char_length': 199, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Brand_name', 'display_name': 'Brand Name', 'requirement_level': 'required', 'conditional_requirement': '', 'ops_tooling': '', 'validation_instructions': 'Always extract brand name', 'definition': 'The official brand name of the product', 'examples': '', 'is_closed_list': 'false', 'acceptable_attributes': {'values': [], 'units': []}, 'is_multi_select': 'false', 'data_type': 'string', 'min_char_length': 1, 'max_char_length': 100, 'min_word': '', 'recommended_words': '', 'min_entries': '', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Part Terminology ID', 'display_name': 'Part Terminology ID', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Identifier for automotive products.', 'examples': '5340', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 1000, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Finish', 'display_name': 'Finish', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.', 'examples': 'Natural; Unfinished; Oak; Satin; High-Gloss; Brushed Chrome; Matte; Brass', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 400, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Resonator Type', 'display_name': 'Resonator Type', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Type or style of Exhaust Resonators', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['Bottle Resonators', 'Bullet Resonators', 'Chambered / Helmholtz Resonators', 'Dissipative Resonators', 'Expansion Chamber Resonators', 'Universal Resonators'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'x', 'display_name': 'Variant Attribute Names', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The designated attribute by which an item is varying.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['assembledProductHeight', 'assembledProductWeight', 'assembledProductWidth', 'count', 'countPerPack', 'dimensions', 'finish', 'inletDiameter', 'manufacturerPartNumber', 'material', 'modelNumber', 'multipackQuantity', 'outletDiameter', 'pieceCount', 'resonator_type', 'vehicleMake', 'vehicleModel', 'vehicleType', 'vehicleYear', 'vehicle_mount_location'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Material', 'display_name': 'Material', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The main material(s) in the product.', 'examples': 'ABS;Aluminum;Brass;Copper;Cast Iron;Polyurethane', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 1000, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Prop 65 Warning Text', 'display_name': 'California Prop 65 Warning Text', 'requirement_level': 'Conditionally Required', 'conditional_requirement': 'If "Isprop65warningrequired" Value = "Yes"', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label. If no Prop 65 warning please leave value blank for this attribute.', 'examples': 'For Example: This product can expose you to chemicals including [name of one or more chemicals], which is [are] known to the State of California to cause [list associated harm(s) e.g. cancer, birth defects or other reproductive harm, etc].', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 5000, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Product Secondary Image URL', 'display_name': 'Additional Image URL', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'Image Validation;1.Quality Resolution: Recommended Pixel Dimensions: 2200px x 2200px. Minimum Pixel Dimensions 1500px x 1500px.;2.When deciding whether to approve supplier provided image that would override an existing image, the new image MUST be of higher or equal quality. The image cannot be approved solely on the basis that it meets all the previous rules. In order to approve, it must meet all rules + be of higher or equal quality.;;;Text Validation;1.The value has to be in URL form;2.URL should not come from DropBox or google drive;3.A value has to be present if attribute is set to required. Supplier remediation is required for items missing images.;4.A value has to go to an image of the item being described, ensure product type is reflected in the imagery.;5.Image should be a high-quality image that is not blurry or have bad photoshopping/editing;a.High-Quality: No excessive wrinkling on bags or glares from plastic packaging.;b.Blurry: Check whether the information/content provided in the image is readable with or without zooming the image. If yes, Mark as no error. Note: Text validation is only performed by Partner/Program team during QC/Production. Checking resolution, contrast, etc. are worked by Imaging team separately as they have separate Tools for it.;c.Cropped Image: NF/NI: We can accept the Facts/Ingredients image which are cropped by supplier from the back side image of the item without any loss of data in the facts/ingredients details. ;d.If associates are doubtful whether there is loss of data in case NF/NI cropped image, then as a next step associate must compare the content of NF/NI image with the back image. If there is no loss of data, then pass else fail it. If the back image is unavailable or unreadable to confirm the then fail the NF/NI image;6.Images with watermarks will need to be removed;7.No heavy/bad shadows.;8.NO "Image Coming Soon", "Image Unavailable", etc. images. ;9.If there are duplicate images, remove the duplicates. If duplicate images have varying image quality i.e., one is blurrier than the other, ensure the higher quality of the duplicates is kept and the lower quality is deleted. If both are of equal quality, simply select one to delete.;10 Images that display explicit nudity, vulgar language, obscene material, sexually suggestive, or pornography, nude or partially nude minors, selfies or imagery shots are prohibited.;11 Images that either portray, glorify or promote in an insensitive way: animal cruelty, any historical or news events, criminal or illegal activity, derogatory stereotyping based on race, ethnicity, gender, sexual orientation, religion, or nationality, hatred, intolerance, natural or man-made disaster(s), tragedy or violence are prohibited.;12. Swatch images should not be part of Product Secondary Image URL.;13. If one of the image URLs under Product Secondary Image is blurry/unclear or doesn’t match with the above Validation Instruction, remove the image and pass the attribute as long as it meets the minimum requirements for image entries.', 'definition': 'Secondary images of the item. File Format: JPEG (.jpg). Recommended Pixel Dimensions: 2200px x 2200px. Minimum Pixel Dimensions for Zoom 1500px x 1500px. Resolution: 300 ppi. Maximum file size: 5MB. Aspect Ratio: 1:1 (Square). Recommended File Naming: GTIN-14 digit\nImage URLs should end in an image file type (.jpg) to follow best practices. They must link to a public image file that loads as an image', 'examples': 'nan', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'URL', 'min_char_length': 1, 'max_char_length': 2500, 'min_word': 'nan', 'recommended_words': '', 'min_entries': '2.0', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Multipack Quantity', 'display_name': 'Multipack Quantity', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be"1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of"1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of"6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of"1." (5) A gift basket of 5 different items has a "Multipack Quantity" of"1."', 'examples': '1;2;4;6', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'Integer', 'min_char_length': 1, 'max_char_length': 4, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Swatch Images|Swatch Variant Attribute', 'display_name': 'Swatch Variant Attribute', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Attribute name corresponding to the swatch.\nEnter the swatch image location in "Swatch Image URL" and its corresponding variant attribute name in "Swatch Variant Attribute". Required for products with visual variations, like color or pattern. List the swatches in the order you recommend they appear on the site.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['assembledProductHeight', 'assembledProductLength', 'assembledProductWeight', 'assembledProductWidth', 'count', 'countPerPack', 'dimensions', 'finish', 'inletDiameter', 'manufacturerPartNumber', 'material', 'modelNumber', 'multipackQuantity', 'outletDiameter', 'pieceCount', 'resonator_type', 'vehicleMake', 'vehicleModel', 'vehicleType', 'vehicleYear', 'vehicle_mount_location'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Compatible Cars', 'display_name': 'Compatible Vehicles', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'List of the vehicles that are compatible with or fit with the item. Used primarily for vehicle parts and accessories. Allows selection using appropriate level of year, make and model information.', 'examples': 'Honda Accord LE 1997; Harley Davidson Fat Boy 2010', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 4000, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Count Per Pack', 'display_name': 'Count Per Pack', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The number of identical items inside each individual package given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of 50. (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labeled for individual sale connected by plastic rings has a "Count Per Pack" of 1. (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of 6. (5) A gift basket of 5 different items has a "Count Per Pack" of 1.', 'examples': '1;2;10;50', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'Integer', 'min_char_length': 1, 'max_char_length': 10, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Manufacturer Part Number', 'display_name': 'Manufacturer Part Number', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.', 'examples': '5061025; TSR-1002; 4-40-3/4-pan-phil', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 60, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Total Count', 'display_name': 'Total Count', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.', 'examples': '1;50;100', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'Integer', 'min_char_length': 1, 'max_char_length': 17, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Main Image URL', 'display_name': 'Main Image URL', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'Image Validation;1.Quality Resolution: Recommended Pixel Dimensions: 2200px x 2200px. Minimum Pixel Dimensions 1500px x 1500px.;2.When deciding wether to approve supplier provided image that would override an existing image, the new image MUST be of higher or equal quality. The image cannot be approved solely on the basis that it meets all the previous rules. In order to approve, it must meet all rules + be of higher or equal quality.;3.Main image should be a silo image (Product shot on a seamless white background [R:255, G:255, B:255]);Text Validation;1.The value has to be in URL form;2.URL should not come from DropBox or google drive;3.A value has to be present if attribute is set to required. Supplier remediation is required for items missing images.;4.A value has to go to an image of the item being described, ensure product type is reflected in the imagery.;5.Image should be a high-quality image that is not blurry or have bad photoshopping/editing;a.High-Quality: No excessive wrinkling on bags or glares from plastic packaging.;b.Blurry: Check whether the information/content provided in the image is readable with or without zooming the image. If yes, Mark as no error. Note: Text validation is only performed by Partner/Program team during QC/Production. Checking resolution, contrast, etc. are worked by Imaging team separately as they have separate Tools for it.;c.Cropped Image: NF/NI: We can accept the Facts/Ingredients image which are cropped by supplier from the back side image of the item without any loss of data in the facts/ingredients details. ;d.If associates are doubtful whether there is loss of data in case NF/NI cropped image, then as a next step associate must compare the content of NF/NI image with the back image. If there is no loss of data, then pass else fail it. If the back image is unavailable or unreadable to confirm the then fail the NF/NI image;6.Images with watermarks will need to be removed;7.No heavy/bad shadows.;8.NO "Image Coming Soon", "Image Unavailable", etc. images. ;9.Main Image URL should not be a Lifestyle Image;a.For exceptions to the rule, Amanda to be notified on the Confluence Page;b.Main image also cannot have any Text or Graphic overlays (Piece Count Info, Best Seller Banners, Feature Callouts, or Graphic Elements, etc. that are SEPARATE from the product itself) Exception to this rule is D4 Private Brand, Great Value Household Paper Products;c.Main image should be a silo image - product shot on a seamless white background;10.If there are duplicate images, remove the duplicates. If duplicate images have varying image quality i.e., one is blurrier than the other, ensure the higher quality of the duplicates is kept and the lower quality is deleted. If both are of equal quality, simply select one to delete.;11 Images that display explicit nudity, vulgar language, obscene material, sexually suggestive, or pornography, nude or partially nude minors, selfies or imagery shots are prohibited.;12 Images that either portray, glorify or promote in an insensitive way: animal cruelty, any historical or news events, criminal or illegal activity, derogatory stereotyping based on race, ethnicity, gender, sexual orientation, religion, or nationality, hatred, intolerance, natural or man-made disaster(s), tragedy or violence are prohibited.', 'definition': 'Main image of the item. File Format: JPEG (.jpg). Recommended Pixel Dimensions: 2200px x 2200px. Minimum Pixel Dimensions for Zoom 1500px x 1500px. Resolution: 300 ppi. Maximum file size: 5MB. Aspect Ratio: 1:1 (Square). Recommended File Naming: GTIN-14 digit\nImage URLs should end in an image file type (.jpg) to follow best practices. They must link to a public image file that loads as an image', 'examples': 'nan', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'URL', 'min_char_length': 1, 'max_char_length': 2500, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Is Primary Variant', 'display_name': 'Is Primary Variant', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['Yes', 'No'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'Boolean', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Occasion', 'display_name': 'Occasion', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The particular target time, event, or holiday for the product.', 'examples': 'Halloween;Christmas;Wedding;Anniversary;Back to School;Birthday;Graduation', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 2000, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Vehicle Make', 'display_name': 'Vehicle Make', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': '1. If a value is present, make sure the value is consistent (if it appears) throughout the base data. ;;2. It is ok if there are blanks for this attribute. ; Curate if possible. This info can be curated/validated by searching base data, including but not limited to; Images, Product Name, Short Description, Long description, Features. It should be clearly displayed in one of these attributes in order to be able to curate accurately.; Do not reach out to the supplier if this value is not available.', 'definition': 'The manufacturer’s marque, under which the vehicle is produced and sold. For example, the Toyota Motor Corporation manufactures the vehicle makes of Toyota, Lexus and Scion. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.', 'examples': 'Ford;BMW;Toyota;KIA', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 4000, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Features', 'display_name': 'Additional Features', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The distinguishing characteristics of a product that describe its appearance, components, and capabilities. Features often highlight the usefulness/advantages of the product to consumers. Input each feature as unique value.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['Adjustable', 'Adjustable Noise Level', 'Ambient Noise Reduction', 'Armless', 'Auto Shut-Off', 'Automatic', 'Belted', 'Bendable', 'Built-In Pump', 'Collapsible', 'Color-Enhancing', 'Compression', 'Convertible', 'Copper-Infused', 'Cord Rewind', 'Corded', 'Cordless', 'Detachable', 'Disposable', 'Double Sided', 'Double-Sided', 'Easy Clean', 'Easy Installation', 'Elevated', 'Eliminates Noise', 'Energy Efficient', 'Engraved', 'Expandable', 'External Lights', 'Fold Out', 'Folding', 'Glow-In-The-Dark', 'Handheld', 'Heavyweight', 'Hinged', 'Illuminated', 'Infrared', 'Insulated', 'Interlocking', 'Internal Lights', 'LED Indicator', 'Lightweight', 'Lockable', 'Long Cord', 'Loud', 'Loud Noise', 'Low Battery Indicator', 'Low Noise', 'Low Noise Level', 'Low Noise Vibration', 'Low Profile', 'Low Wattage', 'Makes Noise', 'Muffled Noise', 'No Rethread Harness', 'Noise Reduction', 'Noise-Canceling', 'Non-Slip Backing', 'OE Replacement', 'Oscillating', 'Patterned', 'Plug-in', 'Programmable', 'Raised', 'Reclining', 'Remote Controlled', 'Removable', 'Reversible', 'Rustproof', 'Safety Strap', 'Security', 'Space-Saving', 'Static Shielding', 'Swivel', 'Telescoping', 'Tiered', 'Top Loading', 'Universal', 'Vehicle Powered', 'Vibrating', 'Water-Resistant', 'Wheeled', 'With Charger', 'With Glow Light', 'With LED Lights', 'With Remote Control'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Sports Team', 'display_name': 'Sports Team', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.', 'examples': 'San Jose Earthquakes;San Jose Sharks;Golden State Warriors;Las Vegas Raiders;San Francisco 49ers;San Francisco Giants;Stanford Cardinal;Oakland Athletics', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 200, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Third Party Accreditation Symbol on Product Package Code', 'display_name': 'Third Party Accreditation Symbol on Product Package Code', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'Validate and Curate certification values on the product package using the image deck.', 'definition': 'The code representing a symbol or marking third party accreditation on the product package.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['100 Percent Canadian Milk', '100% Veganskt', 'ACMI', 'ADCCPA', 'AFIA Pet Food Facility', 'AGRI Confiance', 'AMA Organic Seal', 'ATG', 'Absence of Eggs and Milk is a Certified Allergen Control (CAC)', 'Absence of Eggs', 'Milk and Peanuts is a Certified Allergen Control (CAC)', 'Absence of Peanuts & Almonds is a Certified Allergen Control (CAC)', 'Agence Bio', 'Agri Natura', 'Agriculture Biologique', 'Air cleaner performance standard e.g.', 'humidifiers', 'Aise', 'Aise 2005', 'Aise 2010', 'Aliments BIO Prepares au Quebec', 'Aliments Prepares au Quebec', 'Aliments du Quebec', 'Aliments du Quebec BIO', 'Alpinavera', 'Aluminium Gesamtverband der Aluminiumindustrie', 'Ama Organic Seal Black', 'Ama Seal of Approval', 'American Dental Association', 'American Heart Association Certified', 'Animal Welfare Approved Grassfed', 'Appellation Origine Controlee', 'Approved by the Asthma and Allergy Association', 'Aqua GAP', 'Aquaculture Stewardship Council', 'Arla Farmer Owned', 'Asmi', 'Asthma and Allergy Foundation of America', 'Atlanta Kashrus Commission Certification', 'BC SPCA', 'BDIH Logo', 'BRC Global Standards', 'BSCI', 'Bais Din of Crown Heights Vaad HaKashrus Certification', 'Bebat', 'Belgaqua', 'Benor', 'Berchtesgadener Land', 'Best Aquaculture Practices', 'Best Aquaculture Practices 2 Stars', 'Best Aquaculture Practices 3 Stars', 'Best Aquaculture Practices 4 Stars', 'Beter Leven 1 Ster', 'Beter Leven 2 Ster', 'Beter Leven 3 Ster', 'Better Business Bureau Accredited', 'Better Cotton Initiative', 'Biko Tirol', 'Bio Fisch', 'Bio Gourmet Knospe', 'Bio Knospe', 'Bio Knospe Umstellung', 'Bio Partenaire', 'Bio Ring Allgaeu', 'Bio Solidaire', 'Bio Suisse Knospe', 'Bio Suisse Knospe Umstellung', 'Bio-Bayern with Certificate of Provenance', 'Bio-Bayern without Certificate of Provenance', 'Bio-LABEL Baden-Württemberg', 'Bio-LABEL Hessen', 'Biodegradable', 'Biodegradable Ingredients', 'Biodegradable Products Institute', 'Biodynamic Certification', 'Biogarantie', 'Biokreis', 'Bioland', 'Bioland Ennstal', 'Biopark', 'Bios Kontrolle', 'Bird Friendly Coffee Smithsonian Certification', 'Bk Check Vaad Hakashrus Of Buffalo', 'Blue Angel', 'Blue Ribbon Kosher', 'Bluesign Technologies AG Approved e.g.', 'footwear insoles', 'BonSucro', 'Bord Bia Approved', 'Bord Bia Approved Meat', 'BreatheWay', 'Brewers Association Certified Independent Craft', 'British Retail Consortium Certification', 'Bullfrog Power', 'CAC Absence of Almond', 'CAC Absence of Egg', 'CAC Absence of Milk', 'CAC Absence of Peanut', 'CAN/ BNQ Certified', 'CCA Gluten Free', 'CCF Rabbit Certification', 'CCOF', 'CEBEC', 'CO2 Neutral Certified', 'CO2 Reduceret Emballage', 'CPE Scharrel Eieren', 'CPE Vrije Uitloop Eieren', 'CSA International', 'CSA/NCA - Gluten Free', 'Canada Organic Canadian Organic Logo', 'CanadaGAP program certification mark of conformity', 'Canadian Agricultural Products', 'Canadian Association of Fire Chiefs Approved', 'Canadian Certified Compostable', 'Canadian Eco Logo', 'Canadian Federal Government – CFIA Grade Stamps Canada A e.g. poultry & eggs', 'Canadian Federal Government – CFIA Grade Stamps Canada C e.g. poultry & eggs', 'Canadian Federal Government – CFIA Grade Stamps Canada Utility e.g. poultry & eggs', 'Canadian Federal Government – Dairy Product Grade', 'Canadian Federal Government – Processed Egg Products', 'Canadian Food Inspection Agency (CFIA) as Federally Registered & Inspected Facility - Fish', 'Canadian Food Inspection Agency (CFIA) as Federally Registered & Inspected Facility e.g.', 'meat', 'Canned in Canada to describe green beans', 'Carbon Footprint Standard', 'Carbon Neutral', 'Carbon Neutral NCOS Certified', 'Carbon Neutral packaging', 'Caribbean Kosher', 'Caring Consumer PETA', 'Celiac Sprue Association', 'Central Rabbinical Congress Kosher', 'Certified Angus Beef', 'Certified B Corporation', 'Certified California Sustainable Vineyard and Winery (CCSW)', 'Certified Carbon Free', 'Certified Humane', 'Certified Humane Organisation', 'Certified Naturally Grown', 'Certified Naturally Grown (CNG)', 'Certified OE 100', 'Certified Organic Baystate Organic Certifiers', 'Certified Organic By Organic Certifiers', 'Certified Paleo', 'Certified Paleo Friendly', 'Certified Sustainable Wine of Chile', 'Certified WBENC', 'Certified Wildlife Friendly', 'Cheese – World Champion Cheese Contest', 'Ches K', 'Chicago Rabbinical Council', 'Cincinnati Kosher', 'Claro Fair Trade', 'Climate Neutral', 'Cocoa Life', 'Consumer Choice Award', 'Corrugated Recycles', 'Cosmebio', 'Cotton made in Africa', 'Council of Orthodox Rabbis of Greater Detroit Certification', 'Cradle To Cradle', 'Crossed Grain Symbol', 'Culinarium', 'DLG Award', 'DZG Gluten Free', 'Dallas Kosher Certification', 'Dansk IP Kvalitet', 'Dansk Maelk', 'Deer Protection Program', 'Delinat', 'Demeter Label', 'Dermatology Review Panel ("DRP") independent dermatologists review', 'Design for the Environment (DfE)', 'Diamond K - Massachusetts Certification', 'Direct Trade', 'Distilled in Canada to describe bottled water', 'Dolphin Safe', 'Donau Soya Standard', 'ECARF Seal', 'ECOCert Cosmos Natural', 'ECOCert Cosmos Organic', 'ECOLAND', 'EarthKosher Kosher Certification', 'Earthsure', 'Eco Kreis', 'Eco Label Ladybug', 'Eco Logo', 'EcoCert Certificate', 'Ecogarantie', 'Ecovin', 'Eczema Society of Canada- Accepted', 'Eesti Okomark', 'Eesti Parim Toiduaine', 'Eko', 'Enec', 'Energy Star', 'Epeat Bronze', 'Epeat Gold', 'Epeat Silver', 'Equal Exchange Fairly Traded', 'Equalitas Sustainable Wine', 'Erde Saat', 'Erkend Streek Product', 'Ethical Tea Partnership', "Fair 'n Green", 'Fair Flowers Fair Plants', 'Fair Food Program Label', 'Fair Trade USA', 'Fair Trade USA Ingredients', 'Fair for Life', 'Fair trade mark', 'FairTSA', 'FairTrade Cotton', 'FairTrade Sugar', 'Fairtrade Sourced Ingredient Cashew Nuts', 'Fairtrade Sourced Ingredient Cocoa', 'Fairtrade Sourced Ingredient Coconut', 'Fairtrade Sourced Ingredient Dried Apricots', 'Fairtrade Sourced Ingredient Green Tea', 'Fairtrade Sourced Ingredient Honey', 'Fairtrade Sourced Ingredient Lime Juice', 'Fairtrade Sourced Ingredient Mango Juice', 'Fairtrade Sourced Ingredient Olive Oil', 'Fairtrade Sourced Ingredient Pepper', 'Fairtrade Sourced Ingredient Quinoa', 'Fairtrade Sourced Ingredient Rice', 'Fairtrade Sourced Ingredient Roses', 'Fairtrade Sourced Ingredient Tea', 'Fairtrade Sourced Ingredient Vanilla', 'Falken', 'Farmers & Growers (Organic Certification UK2)', 'Federal Communications Commission (U.S.)', 'Federally Registered Inspected Canada', 'Fidelio', 'Finnish Heart Symbol', 'Finnish Pork Meat Certification System', 'Fish Wise Certification', 'Flamme Verte', 'Flandria', 'Food Alliance Certified', 'Food Federation', 'Food Justice Certified', 'Food Safety System Certification 22000', 'Foodland Ontario', 'For Life (Social Responsibility)', 'Forest Products - Product Lines From a Certified Forest', 'Forest Stewardship Council 100 Percent', 'Forest Stewardship Council Label', 'Forest Stewardship Council Mix', 'Forest Stewardship Council Recycled', 'Foundation Art', 'Freiland', 'Freshcare', 'Friend of the Sea', 'GAA', 'GCP – Global Coffee Platform', 'GFCO', 'GFCP', 'GIG - Gluten Free Foodservice', 'GMO Guard from Natural Food Certifiers', 'GMO Marked', 'GMP Certified', 'GMP ISO 22716', 'GRASKEURMERK', 'GRASP – Risk Assessment on Social Practise', 'Gaskeur', 'Gastec', 'Gebana', 'Gepruefte Sicherheit', 'Gezondere Keuze', 'Global Care', 'Global Gap', 'Global Organic Latex Standard', 'Global Organic Textile Standard', 'Gluten-free Certified: Allergen Control Group/ National Foundation for Celiac Awareness (NFCA)', 'Glycaemic Index Foundation', 'Glycaemic Research Institute', 'Good Housekeeping', 'GoodWeave', 'Green America Certified Business', 'Green Dot', 'Green Restaurant Association Endorsed', 'Green Seal', 'Green Seal Certified', 'Green Shield Certified', 'Green Star Certified', 'Green-e Energy', 'Green-e Energy Certified', 'GreenChoice', 'Groen Label Kas', 'Guaranteed Irish', 'Halal Certification Services', 'Halal Certification Services', 'CH', 'Halal Correct', 'Halal Islamic Food and Nutrition Council of Canada (IFANCC)', 'Halal Islamic Society of North America (ISNA)', 'Halal Plus', 'Haute Valeur Environnementale', 'Hazard Analysis Critical Control Point', 'Health Check', 'Heumilch', 'Hochstamm Suisse', 'Humane Heartland', 'Hypertension Canada Medical Device', 'ICADA', 'ICEA', 'IFANCA Halal', 'IFOAM', 'IFS HPC', 'IGP', 'IHTK Seal', 'IKB EIEREN', 'IKB KIP', 'IKB VARKEN', 'INSTITUT FRESENIUS', 'INT Protection', 'IP Suisse', 'ISEAL Alliance', 'ISO Quality', 'IVN Natural Leather', 'IVN Natural Textiles Best', 'IVO Seal – International Verified Omega-3 e.g.', 'Fish oil', 'Imported in bulk and packaged in Canada', 'Indeklima Maerket', 'Integrity and Sustainability Certified', 'International Aloe Science Council Certificate', 'International Taste & Quality (iTQi) Superior Taste Award', 'Intertek Certificate', 'Intertek Electrical Testing Laboratories (ETL) Mark', 'Jay Kosher Pareve', 'KAT', 'KEMA KEUR', 'KIWA', 'KOF K Kosher', 'KOMO', 'KSA - Kosher', 'KSA D - Kosher Dairy', 'KVBG Approved', 'KVH Kosher', 'Kashruth Council of Canada Kosher Certification', 'Kehilla Kosher California K', 'Kehilla Kosher Heart K', 'Klasa', 'Kosher - Chicago Rabbinical Council (CRC) Dairy', 'Kosher COR Dairy', 'Kosher COR Dairy Equipment', 'Kosher COR Fish', 'Kosher Certification Service', 'Kosher Check', 'Kosher Chicago Rabbinical Council (CRC) Pareve', 'Kosher Grand Rabbinate of Quebec Parve', 'Kosher Inspection Service India', 'Kosher KW Young Israel Of West Hempstead', 'Kosher Kosher Greece', 'Kosher Kosher Madrid Spain', 'Kosher OK Dairy', 'Kosher Oregon Kosher', 'Kosher Organics', 'Kosher Orthodox Jewish Congregation Parve', 'Kosher Ottawa Vaad HaKashrut Canada', 'Kosher Parve Natural Food Certifier', 'Kosher Peru Certification', 'Kosher Star-K -Parve and Passover', 'Kosher Star-K-Parve', 'Kosher Supervisors of Wisconsin Certification', 'Kosher Western Kosher (WK)', 'KosherMex', 'Krav Mark', 'LACON', 'LGA', 'LVA', 'Label Rouge', 'Label of the Allergy and Asthma Federation', 'Laendle Qualitaet', 'Leaping Bunny', 'Lodi Rules "Certified Green"', 'London Beth Din Kosher', 'MCIA Organic', 'MEHR WEG', 'MPS A', 'Made Green in Italy', 'Made in Canada', 'Made in Canada (a)', 'Made in Canada from Domestic and Imported Ingredients', 'Made with 100% Canadian Oats', 'Made with Canadian (ingredient)', 'Made with Canadian Beef', 'Made with Canadian Mustard Seeds', 'Made with Ontario Pork', 'Marine Stewardship Council label', 'Max Havelaar', 'Midwest Kosher', 'Minnesota Kosher Council', "Mom's Choice Award", 'Montreal Vaad Hair Mk Pareve', 'Mortadella Bologna', 'Mundusvini Gold', 'Mundusvini Silver', 'My Climate', 'NAOOA Certified Quality', 'NDOA', 'NF Marque', 'NMX', 'NOM', 'NPA', 'NSF', 'NSF Certified For Sport', 'NSF Gluten Free', 'NSF Non GMO True North', 'NSF Sustainability Certified', 'National Eczema Association Seal of Acceptance', 'Natrue Label', 'Natura- Beef', 'Natura-Veal', 'Nature Care Product', 'Nature Et Progres', 'Natureplus', 'Naturland', 'Naturland Fair Trade', 'Naturland Wildfish', 'Neuland', 'No Product Package Certifications Apply', 'Non GMO Project', 'Non-GMO By Earthkosher', 'Nyckelhalet', 'OCIA', 'OEKO Control', 'OEKO Kreislauf', 'OEKO Quality Guarantee Bavaria', 'OEKO-Tex Made in Green', 'OK Compost Home', 'OK Compost Industrial', 'OK Compost label by Vincotte', 'OK Kosher Certification', 'OKOTEST', 'ORBI', 'OU Kosher Dairy', 'OU Kosher Fish', 'OU Kosher Meat', 'OU Kosher Passover', 'Ocean Wise Recommended Sourced Responsibly', 'Oeko Tex Label', 'Official Eco label Sun', 'On the way to planetproof', 'Ontario - Localize', 'Ontario approved legend - Inspected meat products', 'provincially licensed plants', 'Oregon LIVE (Low Input Viticulture and Enology)', 'Oregon Tilth', 'Organic 100 Content Standard', 'Organic Certifying Body - Centre for Systems Integration', 'Organic Certifying Body - International Certification Services', 'Inc. (ICS)', 'North Dakota', 'USA', 'Organic Certifying Body - JAS (Japanese Agricultural Standards)', 'Organic Certifying Body - OTCO (OREGON TILTH CERTIFIED ORGANIC)', 'Oregon', 'U.S.A.', 'Organic Certifying Body – Agricultural Services Certified Organic (ASCO)', 'Organic Certifying Body – Debio', 'Organic Certifying Body – EcoCert', 'Quebec', 'Canada', 'Organic Certifying Body – Global Organic Alliance (GOA)', 'Ohio', 'U.S.A.', 'Organic Certifying Body – Letis S.A.', 'Organic Certifying Body – Organisme de Certification Québec Vrai (OCQV)', 'Organic Certifying Body – Pro-Cert Organic Systems', 'Saskatchewan', 'Canada', 'Organic Certifying Body – Province of British Columbia Certified Organic', 'British Columbia Association for Regenerative Agriculture (BCARA)', 'Canada', 'Organic Certifying Body – Quality Certification Services (QCS)', 'Florida', 'U.S.A.', 'Organic Certifying Body – Trans Canada Organic Certification Services (TCO)', 'Saskatchewan', 'Canada', 'Organic Certifying Body –Pacific Agricultural Certification Society (PACS)', 'British Columbia', 'Canada', 'Organic Cotton', 'Orthodox Rabbinical Board', 'Orthodox Union Kosher', 'Ozone Friendly General Claim', 'PCO', 'PEFC', 'PEFC Certified', 'PEFC Recycled', 'Paleo Approved', 'Paleo By Earthkosher', "Parent Tested Parent Approved (PTPA) Winner's Seal of Approval", 'Pet to Pet', 'Plastic Free Trust Mark', 'Prepared in Canada', 'Pro Specie Rara', 'Pro-Terra Non-GMO Certification', 'Processed in Canada', 'Proderm', 'Product of Canada', 'Product of the Vinho Verde Region', 'Product of the Year', 'Consumer Survey', 'Produit en Bretagne', 'Protected Designation of Origin', 'Protected Geographical Indication', 'Protected Harvest Certified', 'Proudly Canadian', 'Proven Quality Bavaria', 'Puhtaasti Kotimainen', 'QAI', 'QS', 'Qualenvi', 'Qualitaet Tirol', 'Quality Rhoen', 'RAL Quality Candles', 'REGIONALFENSTER', 'RHP', 'RSB – Roundtable on Sustainable Biomaterials', 'RT-QuIC', 'RT-QuIC Tested', 'Rabbinical Council Of British Columbia BCK', 'Rabbinical Council Of California', 'Rabbinical Council Of New England', 'Rainforest Alliance', 'Real California Cheese', 'Real California Milk', 'Real Food Seal', 'Recognized Canadian Dermatology Association Skin Health', 'Recognized Canadian Dermatology Association Sun Protection', 'Recupel', 'Recyclable General Claim', 'Refined in Canada', 'Roasted and blended in Canada to describe coffee', 'Round Table on Responsible Soy', 'SCS Sustainably Grown', 'SGS Organic', 'SKG 1 star', 'SKG 2 star', 'SKG 3 star', 'SKG Certificate', 'SLG Child Safety', 'SLG Type Tested', 'SLK Bio', 'SOIL Organic Cosmos', 'SOSTain', 'STAR K Kosher', 'SUS', 'Safe Feed Safe Food', 'Safe Quality Food', 'Salmon Safe Certification', 'Schweizer Allergie Gütesiegel', 'Schweizer Alpprodukt', 'Schweizer Bergprodukt', 'SeaChoice', 'Seedling', 'Shopper Army', 'Society of the Plastics Industry (SPI)', 'Soil Association Organic Symbol', 'Star D Kosher', 'Steel Recycling', 'Stellar Certification Services', 'Stiftung Warentest', 'Stop Climate Change', 'Streekproduct.be', 'Suisse Garantie', 'Sunshine State Kosher', 'Sustainable Forestry Initiative', 'Sustainable In Practice (SIP)', 'Sustainable Palm Oil RSPO Certified', 'Sustainable Palm Oil RSPO Credits', 'Sustainable Palm Oil RSPO Mixed', 'TCO Development', 'TNO Approved', 'TOOTHFRIENDLY', 'Tarnopol Kashrus Kosher', 'Terra Vitis', 'The EcoLogo Certification', 'The Fair Rubber Association', 'The Natural and Organic Awards', 'Three Line Kosher', 'Traditional Specialty Guaranteed', 'Transport Canada National Safety Mark (NSM)', 'Triangle K Certification', 'Triman', 'True Foods Canada Trustmark', 'True Source Certified', 'U.S. Department of Agriculture (USDA) – Grade A e.g.', 'eggs', 'UNSER Land', 'USDA', 'USDA Grade - AA', 'USDA Inspection', 'USDA Organic', 'UTZ Certified', 'UTZ Certified Cocoa', 'Underwriters laboratory', "Underwriters' Laboratory Certified", 'Canada "C" and U.S. US', 'Uniquely Finnish', 'United Egg Producers Certified', 'V Label Vegan', 'V Label Vegetarian', 'V.I.V.A.', 'VDE', 'VDS Certificate', 'VEILIG WONEN POLITIE KEURMERK', 'Vaad Hakashrus of Denver Scroll K Certification', 'Vaad Hoeir Kosher', 'Vaelg Fuldkorn Forst', 'Vegan - VegeCert', 'Vegan Awareness Foundation', 'Vegan By Earthkosher', 'Vegan Natural Food Certifiers', 'Vegan Society Vegan Logo', 'Vegaplan', 'Vegetarian Society - Vegetarian', 'Vignerons en Développement Durable (VDD)', 'WEIDEMELK', 'WINERIES for Climate Protection (WfCP)', 'WSDA', 'WWF Panda Label', "Water Quality Association (WQA's)- Tested and Certified", 'Weight Watchers Endorsed', 'Whole Grain 100 Percent Stamp', 'Whole Grain Basic Stamp', 'Whole Grain Council Stamp', 'Whole grains 50 percent Stamp', 'Zero Waste Business Council Certified'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'AAIA Brand ID', 'display_name': 'AAIA Brand ID', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'unique identifier for certain automotive brands', 'examples': 'BCWZ', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 4, 'max_char_length': 4, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Assembled Product Length', 'display_name': 'Assembled Product Depth', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The depth of the fully assembled product. NOTE: This information is shown on the item page as length of the product.', 'examples': '5;10;16;22', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['in', 'ft', 'cm', 'mm', 'sq ft']}, 'is_multi_select': '', 'data_type': 'Decimal', 'min_char_length': 1, 'max_char_length': 16, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 2}, {'attribute_name': 'Warranty URL', 'display_name': 'Warranty URL', 'requirement_level': 'Conditionally Required', 'conditional_requirement': 'If "Has Written Warranty" Value = "Yes - Warranty URL"', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': "The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 5 MB.", 'examples': 'http://www.walmart.com/warranty_info.pdf', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'URL', 'min_char_length': 1, 'max_char_length': 2500, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Outlet Diameter', 'display_name': 'Outlet Diameter', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The diameter of the outlet opening. Measurement is important in products such as water pumps, and mufflers.', 'examples': '12 inch', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['in']}, 'is_multi_select': '', 'data_type': 'Decimal', 'min_char_length': 1, 'max_char_length': 17, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 3}, {'attribute_name': 'Manufacturer', 'display_name': 'Manufacturer Name', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The name of the manufacturer.', 'examples': 'Procter & Gamble; Apple; Sony; General Motors; Yamaha', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 60, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Variant Group ID', 'display_name': 'Variant Group ID', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Required if item is a variant. Make up a number and/or letter code for "Variant Group ID" and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.', 'examples': 'HANESV025', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 300, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Model', 'display_name': 'Model Number', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Model numbers\xa0allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed.\xa0Model numbers\xa0are often found on the bottom, back, or side of a product.\xa0Having this information allows customers to search for items on the site and informs product matching.', 'examples': 'G62-465DX; MFP00112BBQN', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 60, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Vehicle Mount Location', 'display_name': 'Vehicle Mount Location', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': '1. If the value is present, make sure the value is consistent (if it appears) throughout the base data. If there is a mismatch between the attribute value and the base data then only curate and update the correct value to make it consistent.2. If value cannot be found or is missing, please do not mark the item with error.3. Please curate from the base data, if value is not provided by the supplier and is blank. Do not reach out to the supplier if this value is not available.', 'definition': 'The location where the product is installed on the vehicle.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['Bumper', 'Dashboard', 'Door', 'Driver Side', 'Engine', 'Front', 'Front Bumper', 'Front Lower', 'Front Seat', 'Fuel Tank', 'Headrest', 'Hitch', 'Left', 'Passenger Side', 'Rear', 'Rear Bumper', 'Rear Windshield', 'Right', 'Roof', 'Seat', 'Trunk', 'Window'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Condition', 'display_name': 'Condition', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'This refers to the state of an item, indicating its level of wear, prior use (if any), appearance, and overall quality. Availability of condition types may vary by product/category as well as by Seller eligibility per program. For more details, review this Help article: https://sellerhelp.walmart.com/seller/s/guide?language=en_US&article=000011205.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['New', 'Pre-Owned: Fair', 'Pre-Owned: Good', 'Pre-Owned: Like New', 'Remanufactured'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Items Included', 'display_name': 'Items Included', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Notes what items are sold with an item and part of a set.', 'examples': 'Caps; Cover; Cable; Fastener', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 200, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Vehicle Type', 'display_name': 'Vehicle Category', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'Curate if possible.; This info can be curated/validated by searching base data, including but not limited to Product Name, Short Description, Long description, images, Title, features, product_line', 'definition': 'Grouping of different kinds of vehicles based on use and form. Important selection criteria, especially for compatibility, for products including boat components, tires and auto accessories.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['All-Terrain Vehicle', 'Boat', 'Bus', 'CUV', 'Campervan', 'Car', 'Crossover & Minivan', 'Dirt Bike', 'Go-Kart', 'Golf Cart', 'Helicopter', 'Lawn Mower', 'Motor Vehicle', 'Motorcycle', 'Plane', 'Popup Camper', 'Recreational Vehicle', 'Snowmobile', 'Sport Utility Vehicle', 'Tractor', 'Trailer', 'Truck', 'Utility Task Vehicle'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Vehicle Model', 'display_name': 'Vehicle Model', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': '1. If a value is present, make sure the value is consistent (if it appears) throughout the base data. ;;2. It is ok if there are blanks for this attribute. ;; Curate if possible. This info can be curated/validated by searching base data, including but not limited to:; Images, Product Name, Short Description, Long description, Features. It should be clearly displayed in one of these attributes in order to be able to curate accurately.;; Do not reach out to the supplier if this value is not available.', 'definition': 'The manufacturer’s name/letter/number designation given to a particular design or series of vehicles with similar characteristics. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.', 'examples': '240Z; 2000 Sunbird; Mustang V6 Fastback;X5', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 4000, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Piece Count', 'display_name': 'Number of Pieces', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits.) Examples: A gift basket of 5 different items has a “Piece Count” of 5.; A 105-Piece Socket Wrench set has a "Piece Count" of 105.; A 500-piece puzzle has a “Piece Count” of 500.', 'examples': '5;105;500', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'Integer', 'min_char_length': 1, 'max_char_length': 17, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Product Net Content Parent|Product Net Content UOM', 'display_name': 'Unit', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The unit of measure as defined on the product labeling. For example, a gallon of milk has 128 Fluid Ounces: Measure = 128 and Units = Fluid Ounces.\n\nPlease Note: If your item includes multiple items with the same UOM, add them together to get the total measurement. If the item is a set with multiple items that do not share the same UOM, list as 1 Each.\nThe quantity (or quantities) of the product contained in the package along with its unit of measure typically printed on the label for the country or market where the product is sold.  For example, a gallon of milk has 128 Fluid Ounces: Measure = 128 and Units = Fluid Ounces. \n\nPlease Note: If your item includes multiple items with the same UOM, add them together to get the total measurement. If the item is a set with multiple items that do not share the same UOM, list as 1 Each.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['Case', 'Centiliter', 'Centimeter', 'Count', 'Cubic Foot', 'Each', 'Fluid Ounces', 'Foot', 'Gallon', 'Gram', 'Inch', 'Kilogram', 'Liter', 'Meter', 'Milliliter', 'Ounce', 'Pallet/Unit Load', 'Pint', 'Pound', 'Quart', 'Quart Dry', 'Square Foot', 'Yard'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Assembled Product Weight', 'display_name': 'Assembled Product Weight', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The weight of the fully assembled product.', 'examples': '1.75;5;12;32', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['oz', 'lb']}, 'is_multi_select': '', 'data_type': 'Decimal', 'min_char_length': 1, 'max_char_length': 16, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 2}, {'attribute_name': 'Dimensions', 'display_name': 'Dimensions', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Appropriate dimensions for the product, from the consumer point of view.', 'examples': '32 in x 72 in x 42 in\n1.6 in x 3 in', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 4000, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Net Content Statement', 'display_name': 'Net Content Statement', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Complete product net content statement, as it appears on product packaging.', 'examples': '6 - 5.3 OZ (150g) CUPS;1.98 Lb (900g)', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 500, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Inlet Diameter', 'display_name': 'Inlet Diameter', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': "This is the diameter of a product's inlet.  Measured in Inches.", 'examples': '2.5 inch', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['in']}, 'is_multi_select': '', 'data_type': 'Decimal', 'min_char_length': 1, 'max_char_length': 17, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 3}, {'attribute_name': 'Product Long Description', 'display_name': 'Key Features', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': "1.  The Product Name can be present in the Long Description in bold above the bullet points. When there is a difference, then the Product Name should be updated in the Long Description from the Product Name attribute.  This is not required, but if it is present, please leave it. \n2.  The Long Description should be in bullet point form \n3.  Remove Seller/Supplier Specific promotional text like warranty, free shipping or discounts.             \n              - Examples:  20% off! / Free shipping / 2 Day Shipping / Satisfaction Guaranteed / 90 Day Warranty\n     It is okay to leave promotional text that talks about other items included in the product line, such as accessories, other colors or variations available, other products they manufacturer that work with the item, etc.\n              - Example:  Google Home works with Philips Hue Lightbulbs and the NEST thermostat.\n              - Example:  Comes in White, Grey and Black colors.\n4.  Remove text that promotes another retailer or redirects you to another website.  It should not have any mention of another retailer unless it is related to the product.  Any Blacklisted/Superlative/Competitive words to be removed as a part of the Long Description. \n              - Example of what isn't allowed:  Buy on Amazon!\n              - Example of what isn't allowed: For more information follow this link - XYZ.com\n              - Example of what IS allowed: Works with Amazon Alexa. \n5.  Please leave trademarked or copyright symbols. Please remove symbols like: €,…,†,‡,•,¢,£,¥,±,¶,~,â, ???\n6.  The Long Description should NOT be in ALL CAPS or all lower case. The Long Description should be in proper casing. Brands that have been validated in Walle that are in ALL CAPS or all lower case are acceptable. Trademarked or copyright information that is in ALL CAPS or all lower case is acceptable as well.\n7.  The Long Description should not be a repeat of the Short Description, even if in list form.  If a repeat, one should fail Long Description only.\n8.  You may delete portions of the Long Description or Short Description that are repeated.  For example, if a bullet point in the Long Description is also a sentence in the Short Description, you may delete one.  If it talks about the product or Brand/ Manufacturer story, leave it in the short description.  If it is a Feature, Benefit or Specification, leave it in the Long Description.\n9.  Long Description is assigned to describe the product in bullet points. The minimum number of bullet points required to describe the products will be defined by the ME team at the PTG level. Long Description that contains brand stories or manufacturer story (ie  a paragraph that defines the brand in more than two sentences. The sentences should be talking only about the Brand) should be removed and appended at the end of the Short Description.  If the Brand Story exists in the Long Description then just create a new paragraph and paste the brand story at the end into the Short Description.  If the Brand story of Manufacturer story is not in paragraph format, reject the Long Description and Short Description and seek supplier remediation for a fix.\n10.  If the entire Long Description  is in paragraph form and talks about the product or Brand/ Manufacturer story and the Short Description is empty, you should move the Long Description to the Short Description attribute and fail the Long Description.\n11.  If a portion of the Long Description is in paragraph form and describe the product and brand/manufacturer story, you should move that portion of the Long Description to the Short Description attribute.\n12.  Read through and look at the bullet points. Is it clear what you would be buying? Does it provide details the item being sold? If it does not fail, Long Description.\n13.  Curation of grammatical errors and special characters is allowed.\n14.  Leave in HTML if it is currently on the Long Description. Do not format existing HTML. If Long Description text is not in the HTML format operations team will format it into HTLM using the HTML editor. (Tab based formatting, space-based formatting, line based formatting placed into the HTML editor will not create HTML tags and create the HTML format).  Do not fail if missing HTML.\n15.  Incoming formatted data:\n     1. HTML based data in bullet form will be accepted and in paragraph format will be rejected. Supplier remediation is required.\n     2. Non-HTML based format will be edited to HTML format only if in bullets. Paragraph will be rejected. Supplier remediation is required.\n16. Spell check should be performed on the Long Description.\n17.  Any nutrient content/ dietary need claim or certification listed from the supplier MUST be identified on the product package.  To validate this claim or certification you will need to verify using the product images.\n\nDo not create any new copy.", 'definition': 'Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format.', 'examples': 'OE-style louvered tubes (where applicable) help reduce radiated noise for factory-style sound quality;Engineered to fit, perform and sound like the original system;Durable aluminized steel construction and helps reduce corrosion;Features thick OE-style flanges, hardware and brackets for strength.', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 10000, 'min_word': 'nan', 'recommended_words': '', 'min_entries': '4.0', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Product Net Content Parent|Product Net Content', 'display_name': 'Measure', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The numeric net content portion that appears on an item. \n\nPlease Note: If your item includes multiple items with the same UOM, add them together to get the total measurement. If the item is a set with multiple items that do not share the same UOM, list as 1 Each.\nThe quantity (or quantities) of the product contained in the package along with its unit of measure typically printed on the label for the country or market where the product is sold.  For example, a gallon of milk has 128 Fluid Ounces: Measure = 128 and Units = Fluid Ounces. \n\nPlease Note: If your item includes multiple items with the same UOM, add them together to get the total measurement. If the item is a set with multiple items that do not share the same UOM, list as 1 Each.', 'examples': '1;12;32', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'Decimal', 'min_char_length': 1, 'max_char_length': 16, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 3}, {'attribute_name': 'Automotive Parts Division', 'display_name': 'Automotive Parts Division', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Automotive Parts Division attribute helps the automotive parts team to sequester their various assortments into the appropriate reporting hierarchy nodes.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['Direct Replacement', 'Mechanic Tools', 'Motorcycle', 'OEM Replacement', 'Off-road', 'Performance', 'Powersport', 'RV & Trailer'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Has Written Warranty', 'display_name': 'Has Written Warranty', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Indicates an item has written warranty documentation included on the product label, inside the product packaging, or a website URL or PDF available for customer review. Pursuant to California Civil Code Section 1793.01 (The Song-Beverly Consumer Warranty Act), an express warranty for consumer goods sold within California must not commence earlier than the date of delivery of the item to the consumer. If applicable, please review and update the warranty terms for this item.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['No', 'Yes - Warranty Text', 'Yes - Warranty URL'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Assembled Product Height', 'display_name': 'Assembled Product Height', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The height of the fully assembled product.', 'examples': '5;10;16;22', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['in', 'ft', 'cm', 'mm', 'sq ft']}, 'is_multi_select': '', 'data_type': 'Decimal', 'min_char_length': 1, 'max_char_length': 16, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 2}, {'attribute_name': 'Brand', 'display_name': 'Brand Name', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': '1.\tIf the value is found in Walle, please make sure that the values match the style format as mentioned in Walle. If the style format does not match, please correct the value in the base data sheet as per the information in Walle. \n2.\tIf the value isn\'t found in Walle, please mark Brand as an error and continue to validate the item. Make sure when the Brand name is fixed post supplier communication, we will fix the Brand name in the dependent attributes like Product Name, Short and Long Description.  \n3.\tUnbranded is acceptable as a Brand if it matches the rest of the base data (Product Name, Long Description, Short description, Images).  Do not add it to the Product Name.\n4.\tPlease fix Brand name in all dependent attributes (Product Name, Short and Long Description).\n5.\tIf the value is "Unbranded", "Generic" "N/A", "Unknown", "Online", "unbranded", "unbrand", "none", "general", “@generated" please try to curate the Brand from the base data. If it can\'t be done, please list as Unbranded (see rule 3 before designating as Unbranded) and continue to validate. Make sure when the Brand name is fixed by curating from base data or post supplier communication, we will have to fix the Brand name in the dependent attributes like Product Name, Short and Long Description. \n6.\tIf the Brand listed in the brand value attribute does not match with the brand provided in the base data consistently (Example: Crayola is listed as Brand, but the Brand "Sharpie" is the only Brand listed in base data across product name, short and long description. Check in Google to see if Crayola and Sharpie are connected and if not, please correct the Brand attribute to "Sharpie" and validate via Walle). However, if there is a discrepancy across base data (product name has Sharpie and description has Crayola) please reach out to the supplier for correct brand name.\n7.\tSame as above scenario. Check in Google to see if Crayola and Sharpie are connected and if they are then do not delete any other Brands. Example: Crayola is listed as the Brand value; however, Sharpie is mentioned in the base data ALONG WITH Crayola. You will only need to validate one Brand in the brand value attribute, not both. Do not delete any other Brands from any base data.', 'definition': 'The name provided by the brand owner that is intended to be recognized by the consumer as represented on the product. If item does not have a brand, use "Unbranded".  This should be the brand ONLY, not brand + product line. Make sure brand name is spelled, punctuated & capitalized properly according to the brand itself.', 'examples': 'Camco;Lippert;STROMBER;DriveWorks;Reliable Aftermarket Parts Inc.;8TEN;PlushDeluxe;Kritne;Unique Bargains', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 60, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Is Prop 65 Warning Required', 'display_name': 'Is Prop 65 Warning Required', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Selecting "Yes" indicates the product requires California\'s Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['Yes', 'No'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'Boolean', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Size', 'display_name': 'Size', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Size of an item.', 'examples': '12 in; 5 ft; 25 mm', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 500, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Assembled Product Width', 'display_name': 'Assembled Product Width', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'The width of the fully assembled product.', 'examples': '5;10;16;22', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['in', 'ft', 'cm', 'mm', 'sq ft']}, 'is_multi_select': '', 'data_type': 'Decimal', 'min_char_length': 1, 'max_char_length': 16, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 2}, {'attribute_name': 'Swatch Images|Swatch Image URL', 'display_name': 'Swatch Image URL', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'URL of the color or pattern swatch image. This is required for products with visual variations and will be shown as a small circle on the item page. File Format: JPEG (.jpg). Recommended Pixel Dimensions: 100px x 100px. Resolution: 300 ppi. Maximum file size: 5MB. Recommended File Naming: GTIN-14 digit. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required.)\nEnter the swatch image location in "Swatch Image URL" and its corresponding variant attribute name in "Swatch Variant Attribute". Required for products with visual variations, like color or pattern. List the swatches in the order you recommend they appear on the site.', 'examples': 'http://www.walmart.com/somepath/some-image.jpg', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'URL', 'min_char_length': 1, 'max_char_length': 2500, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Sports League', 'display_name': 'Sports League', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.', 'examples': 'NFL;WWE;MLB;NBA;NASCAR;USA Archery', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 100, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Short Description', 'display_name': 'Site Description', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': "1.  The Short Description should be in paragraph form. Single or multiple paragraphs. Category specific exceptions can be processed post agreement with ME and OPS.  For single paragraphs start and end with <p> tag.  All Short Descriptions that contain multiple paragraphs will only be edited using HTML editor without losing the format.  For categories where lists are not permitted in Short Description the list should be removed and should be added it as a part of the Long Description.\n2.  Should be consistent with the other item data (not a paragraph about a different item).\n3.  Remove Seller/Supplier Specific promotional text like warranty, free shipping or discounts.             \n              - Examples:  20% off! / Free shipping / 2 Day Shipping / Satisfaction Guaranteed / 90 Day Warranty\n     It is okay to leave promotional text that talks about other items included in the product line, such as accessories, other colors or variations available, other products they manufacturer that work with the item, etc.\n            - Example:  Google Home works with Philips Hue Lightbulbs and the NEST thermostat.\n            - Example:  Comes in White, Grey and Black colors.\n4.  Remove text that promotes another retailer.  It should not have any mention of another retailer unless it is related to the product. Any Blacklisted/Superlative/Competitive words to be removed as a part of the Short Description. \n            - Example of what isn't allowed:  Buy on Amazon!\n            - Example of what isn't allowed: As seen at BestBuy\n            - Example of what IS allowed: Works with Amazon Alexa.\n5.  Please leave trademarked or copyright symbols. Please remove symbols like: €,…,†,‡,•,¢,£,¥,±,¶,~,â, ???\n6.  The Short Description should NOT be in ALL CAPS or all lower case. The Short Description should be in proper casing. Brands that have been validated in Walle that are in ALL CAPS or all lower case are acceptable. Trademarked or copyright information that is in ALL CAPS or all lower case is acceptable as well.\n7.  The Short Description should not be a repeat of the Long Description, even if in paragraph form.  If a repeat, Long Description should fail.\n8.  You may delete portions of the Long Description or Short Description that are repeated.  For example, if a bullet point in the Long Description is also a sentence in the Short Description, you may delete one.  If it talks about the product or Brand/ Manufacturer story, leave it in the short description.  If it is a Feature, Benefit or Specification, leave it in the Long Description.\n9.  Short Description is assigned to describe the product and brand/manufacturer story. Long Description that contains brand stories or manufacturer story (ie  a paragraph that defines the brand in more than two sentences. The sentences should be talking only about the Brand) should be removed and appended at the end of the Short Description.  If the Brand Story exists in the Long Description then just create a new paragraph and paste the brand story at the end into the Short Description.  If the Brand story of Manufacturer story is not in paragraph format, reject the Long Description and Short Description and seek supplier remediation for a fix.\n10.  If the entire Short Description is in bullet form and talks about features or specifications of the item, and the Long Description is empty, you should move the Short Description to the Long Description attribute and fail the Short Description.\n11.  If a portion of the Short Description is in bullet form and talks about features or specifications of the item, you should move that portion of the Short Description to the Long Description attribute.\n12.  Read through and look at the paragraph. Is it clear what you would be buying?  Does it describe the item being sold? If it does not fail the Short Description. \n13.  Curation of grammatical errors and special characters is allowed.\n14.  Leave in HTML if it is currently on the Short Description.  If Short Description does not contain HTML, this is fine. Do not fail, do not send to supplier communication.\n15. Spell check should be performed on the Short Description\n16.  Any nutrient content/ dietary need claim or certification listed from the supplier MUST be identified on the product package.  To validate this claim or certification you will need to verify using the product images.\n17.  When validating the Minimum Word Count in Short Description, you can pass the attribute if the word count is no more than 5 words short. For instance, if the minimum word count is 60 and an item comes in at 55, it is OK to pass it. If it comes in with 54 words or less, fail it. This gives a -5 word buffer to the Short Description Minimum Word Count set on any PT, regardless of count.\n\nDO NOT CREATE ANY NEW COPY UNDER ANY CIRCUMSTANCES.", 'definition': 'Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.', 'examples': 'Made of high quality stainless steel. Precise design and high performance. Reduce engine exhaust noise effectively,and the high temperature exhaust can discharge smoothly. As an important part of exhaust pipe, high strength and high temperature resistance. Easy to install and durable in use. Stylish design , becoming a dazzling decoration in your car.', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 100000, 'min_word': '55.0', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Vehicle Fitment Type', 'display_name': 'Vehicle Fitment Type', 'requirement_level': 'Required', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Describes If The Fitment Of The Product Is Universal or Vehicle Specific.', 'examples': 'nan', 'is_closed_list': 'true', 'acceptable_attributes': {'values': ['Specific', 'Universal'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 0, 'max_char_length': 0, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Vehicle Year', 'display_name': 'Vehicle Year', 'requirement_level': 'Recommended', 'conditional_requirement': 'nan', 'ops_tooling': '', 'validation_instructions': '1. If a value is present, make sure the value is consistent (if it appears) throughout the base data. ;;2. It is ok if there are blanks for this attribute. ; Curate if possible. This info can be curated/validated by searching base data, including but not limited to; Images, Product Name, Short Description, Long description, Features. It should be clearly displayed in one of these attributes in order to be able to curate accurately.; Do not reach out to the supplier if this value is not available.', 'definition': 'The model year as provided by the manufacturer. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.', 'examples': '2019;1995', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'Integer', 'min_char_length': 1, 'max_char_length': 7, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}, {'attribute_name': 'Warranty Information', 'display_name': 'Warranty Text', 'requirement_level': 'Conditionally Required', 'conditional_requirement': 'If "Has Written Warranty" Value = "Yes - Warranty Text"', 'ops_tooling': '', 'validation_instructions': 'nan', 'definition': 'Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.', 'examples': 'This warranty covers any defects in materials or workmanship, including installation, with the exceptions of fading or discoloration caused by exposure to sunlight or chemicals. This warranty runs for five years from the date your carpet is installed. Counterpoint will either replace your carpet with new carpet of similar composition and price, or refund the full purchase price of your carpet, whichever you prefer. Contact Counterpoint at 800-867-5309.', 'is_closed_list': 'false', 'acceptable_attributes': {'values': ['nan'], 'units': ['nan']}, 'is_multi_select': '', 'data_type': 'String', 'min_char_length': 1, 'max_char_length': 20000, 'min_word': 'nan', 'recommended_words': '', 'min_entries': 'nan', 'recommended_entries': '', 'precision': 0}]