import os
import pandas as pd
from io import BytesIO, StringIO

FILE_READERS = {
    ".csv": pd.read_csv,
    ".json": pd.read_json,
    ".xls": pd.read_excel,
    ".xlsx": pd.read_excel,
}

def get_file_extension(filename: str) -> str:
    return os.path.splitext(filename)[1].lower()

def read_file(filename: str, contents: bytes):
    ext = get_file_extension(filename)
    if ext not in FILE_READERS:
        raise ValueError("Unsupported file type")

    if ext in [".xls", ".xlsx"]:
        return FILE_READERS[ext](BytesIO(contents))
    else:
        return FILE_READERS[ext](StringIO(contents.decode()))
