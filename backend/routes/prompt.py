from fastapi import FastAPI
from pydantic import BaseModel
import google.generativeai as genai
import os
from fastapi import APIRouter

router = APIRouter()   

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyAEIuK5gFR6Wh7v77fe4WJdaCIKWLphBds")
genai.configure(api_key=GEMINI_API_KEY)

model = genai.GenerativeModel("gemini-1.5-pro")


LONG_PROMPT = """
TASK:  
Given a product’s Short Description, Product Name, and Long Description → clean and transform the Long Description following the rules below.  

OUTPUT FORMAT:  
If valid, output must begin with:  
  BEST LONG SUMMARY:  
  • Property Name: description  
  • Property Name: description   

If invalid → output exactly:  
  FAIL LONG DESCRIPTION  

If text is moved to the Short Description, output it clearly under:  
  UPDATED SHORT DESCRIPTION: <new text>  

---

CONTENT RULES:  

1. The summary must contain bullet points.  
2. Each bullet must begin with: "• Property Name: description".  
3. Mandatory bullets (must always appear in the summary): 
   - **Product Name**: Exact match of the Product Name attribute. (do not include the word "Product Name:")
   - **Product Type**: Describe what the product is.  
   - **Application**: Specify the use or intended purpose of the product.  
   - **Utility**: Explain how the product is useful (factual, non-marketing).  
   - **Key Features**: Highlight the standout features of the product.  
   - Include exactly one bullet per mandatory attribute.
   - If an attribute value is missing, skip it. Do not invent placeholders.
   - Include these bullets at the start of the description, in the order listed above.
4. All passed attributes with valid values** (e.g., material, dimensions, weight, brand, compatibility, condition or any other PRODUCT ATTRIBUTES: ) must be included as separate bullets**. Do not skip any valid attribute.
5. Descriptions must remain factual only.  
   - No interpretation, persuasion, or explanations.  
   - No vague/repetitive wording (e.g., “ensures you receive,” “clearly indicates”).  
6. No second-person language (“you,” “your”).  
7. No marketing fluff. Tone must be technical and product-focused.  
8. Grammar, spelling, and casing must always be correct.  
9. If an attribute value is missing, skip it. Do not invent placeholders.  
10. If the format is not bullet points → return FAIL LONG DESCRIPTION.  
11. Content must be consistent with the actual product.  


---

STRUCTURAL RULES:  
1. If the Product Name is present in the Long Description in bold above the bullet points, leave it. If the Product Name differs, update it to match the Product Name attribute.  
2. Long Description must be in bullet point form. Paragraph-only descriptions → reject.  
3. Remove promotional text about discounts, shipping, warranties, or guarantees. Keep factual mentions of accessories, compatibility, or product variations.  
4. Remove external retailer mentions/redirects. Keep factual mentions like “Works with Amazon Alexa.”  
5. Symbols: keep ™, ®, ©. Remove €, †, ‡, •, ¢, £, ¥, ±, ¶, ~, â, ???.  
6. Ensure proper casing. ALL CAPS/lowercase is only allowed if validated Brand names or trademarks.  
7. Do not repeat the Short Description. Delete duplicate sentences across Short/Long descriptions.  
8. Brand/Manufacturer Story Handling:  
   - If in paragraph form (>2 sentences), move it to the end of the Short Description.  
   - If it is about features/specifications, keep in Long Description.  
9. If Long Description is a full paragraph and Short Description is empty → move it to Short Description and fail Long Description.  
10. If only part of the Long Description is a brand/manufacturer paragraph → move that part to Short Description.  
11. Must clearly describe the product. If unclear → fail Long Description.  
12. Correct grammatical/spelling errors.  
13. If HTML is present, leave it unchanged. Do not add/remove tags.  
14. If non-HTML bullet points are present, leave them; Ops team will reformat.  
15. Verify any nutritional/dietary claim against product images. Remove if not visible on packaging.  

---

EXAMPLES:  

• Material: Constructed from carbon steel for strength and durability.  
• Handle Design: Features a cushioned blue grip for comfort and skid resistance.  
• Ratchet Technology: Provides 360° swing for ease of positioning.  
• Application: Designed for disc brake systems with twin and quad piston calipers.  
• Usage: Suitable for both fixed and floating caliper systems.  
• Function: Expands and retracts spreading plates with simple handle movement.  
• Adjustment: Direction controlled by a reversible ratchet switch.  
• Compatibility: Works with single and twin-piston floating calipers.  
• Package Contents: Includes one brake piston compression tool.  
• Intended Use: Practical for professional mechanics and home garages.  

❌ Incorrect Output:  
"FAIL LONG DESCRIPTION" (when rules are not followed).  
"""

SHORT_PROMPT = """
You are an expert product content curator.  
You will validate and clean the Short Description of a product according to strict rules.  
Do not invent or create new content. Only keep, remove, or move text based on the rules below.  
Preserve factual details about the product.  

RULES:  
1. The Short Description should be in paragraph form.  
   - Single or multiple paragraphs allowed.  
   - For single paragraphs, start and end with <p> tag.  
   - Multi-paragraph Short Descriptions must keep paragraph formatting.  
   - If category does not allow lists, remove lists from Short Description and add them to Long Description.  
2. Must be consistent with the actual product. Do not include text about a different item.  
3. Remove promotional text like warranty, free shipping, or discounts.  
   -  Examples: â€œ20% off!â€�, â€œFree shippingâ€�, â€œ90 Day Warrantyâ€�.  
   -  Allowed: mentions of accessories, variations, or compatible products.  
4. Remove text that promotes another retailer.  
   -  Not allowed: â€œBuy on Amazon!â€�, â€œAs seen at BestBuyâ€�.  
   -  Allowed: â€œWorks with Amazon Alexa.â€�  
5. Keep â„¢, Â®, Â©. Remove â‚¬, â€ , â€¡, â€¢, Â¢, Â£, Â¥, Â±, Â¶, ~, Ã¢, ???.  
6. Proper casing required.  
   -  Brand names validated in ALL CAPS/lowercase are allowed.  
   -  Trademark/copyright text in ALL CAPS/lowercase is allowed.  
7. The Short Description should not repeat the Long Description. If repeat, fail the Long Description.  
8. Remove duplicate text across Short and Long Descriptions:  
   - Brand/manufacturer story â†’ leave in Short Description.  
   - Features/specs â†’ leave in Long Description.  
9. Short Description is for describing the product and brand/manufacturer story.  
   - If brand story exists in Long Description (paragraph form >2 sentences), move it to the end of Short Description.  
   - If brand/manufacturer story is not in paragraph form, reject both Short and Long Description and request supplier remediation.  
10. If Short Description is entirely in bullet form (features/specs) and Long Description is empty â†’ move it to Long Description and fail Short Description.  
11. If Short Description partially contains bullet-form features/specs â†’ move those to Long Description.  
12. Short Description must clearly describe the product. If not, fail.  
13. Fix grammar and spelling errors.  
14. Leave HTML if present. Do not reformat or remove.  
15. Spell check is required.  
16. Any dietary/nutritional claims must be verified on product images. Remove if not visible on packaging.  
17. Minimum Word Count rule:  
   - If word count is â‰¥ (min count â€“ 5), pass.  
   - If word count < (min count â€“ 5), fail.  
   Example: Minimum = 60, Accept â‰¥55, Fail â‰¤54.  

TASK INSTRUCTION:  
Given a productâ€™s Short Description, Product Name, and Long Description:  

Clean and transform the Short Description following all rules above.  
If the Short Description fails the rules, output: "FAIL SHORT DESCRIPTION".  
If you move text to the Long Description, clearly mark it under "UPDATED LONG DESCRIPTION".  
Output must follow this format:  

UPDATED SHORT DESCRIPTION:
<cleaned paragraph(s) here>

UPDATED LONG DESCRIPTION (if changed):
<text here or "No change">
"""


class ProductInput(BaseModel):
    product_name: str
    short_desc: str
    long_desc: str
    mode: str  


# --------------------------
# 4. FastAPI App
# --------------------------
app = FastAPI(title="Product Description Cleaner API")


@router.post("/clean_description")
async def clean_description(data: ProductInput):
    if data.mode == "long":
        prompt = LONG_PROMPT
    elif data.mode == "short":
        prompt = SHORT_PROMPT
    else:
        return {"error": "mode must be 'short' or 'long'"}

    final_prompt = f"""{prompt}

Product Name: {data.product_name}
Short Description: {data.short_desc}
Long Description: {data.long_desc}
"""

    response = model.generate_content(final_prompt)
    return {"result": response.text}


