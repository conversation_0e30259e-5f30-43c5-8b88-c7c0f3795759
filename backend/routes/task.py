from fastapi import APIRouter, HTTPException
from google.cloud import storage
import json
import logging
import time
from google.oauth2 import service_account
import os
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import google.generativeai as genai
from googleapiclient.discovery import build

router = APIRouter()  

# Use your existing credentials
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
SERVICE_ACCOUNT_FILE = os.path.join(BASE_DIR, "ops-excellence.json")
credentials = service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE)

storage_client = storage.Client(credentials=credentials, project=credentials.project_id)
bucket = storage_client.bucket("wal_test_bucket")

logger = logging.getLogger(__name__)

# Gemini API configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyAEIuK5gFR6Wh7v77fe4WJdaCIKWLphBds")
genai.configure(api_key=GEMINI_API_KEY)

# ✅ Google Sheets configuration
SPREADSHEET_ID = "1PY6ZJzxz_96jwmgsiht4mTkZv6GKP3GogoTQw3iYR7U"  # Replace with your actual spreadsheet ID

# Models for insights generation
class SiteSummary(BaseModel):
    attribute: str
    selectedSources: List[str] = []
    selectedValues: List[str] = []
    walmartValidation: Optional[str] = ""
    llmValidation: Optional[str] = ""
    walmartComment: Optional[str] = ""
    llmComment: Optional[str] = ""
    finalVerdict: Optional[str] = ""

class TaskInsightRequest(BaseModel):
    item_id: str
    selected_attributes: Optional[List[str]] = []
    generate_summary: bool = True
    include_validations: bool = True

class TaskInsightResponse(BaseModel):
    short_summary: str
    long_summary: str
    attribute_analysis: Optional[Dict[str, Any]] = None
    validation_summary: Optional[Dict[str, Any]] = None

# ✅ Save data models - simplified (Google Sheets only)
class TableRowData(BaseModel):
    attribute: str
    walmart: str = ""
    llm: str = ""
    brand: str = ""
    final_value: str = ""
    final_source: str = ""
    final_verdict: str = ""
    selected: bool = False
    competitor_values: Dict[str, str] = {}
    validation_data: Dict[str, str] = {}
    comment_data: Dict[str, str] = {}

class SaveTaskDataRequest(BaseModel):
    item_id: str
    product_name: str = ""
    category: str = ""
    table_data: List[TableRowData]
    insights_data: Dict[str, str] = {}
    selected_cells: List[str] = []
    save_to_sheets: bool = True

class SaveTaskDataResponse(BaseModel):
    success: bool
    message: str
    sheets_result: Dict[str, Any] = {}
    timestamp: str
    total_rows_saved: int = 0

@router.get("/{item_id}/view")
async def get_task_view(item_id: str):
    """Get task data for viewing"""
    try:
        blob = bucket.blob(f"view/{item_id}.json")
        
        if not blob.exists():
            logger.warning(f"⚠️ Task {item_id} not found in bucket")
            raise HTTPException(status_code=404, detail="Task not found")
            
        json_data = blob.download_as_text()
        task_data = json.loads(json_data)
        
        logger.info(f"✅ Task {item_id} retrieved from gs://wal_test_bucket/view/{item_id}.json")
        
        return {
            "success": True,
            "data": task_data,
            "view_url": f"/task/{item_id}/view",
            "retrieved_from": f"gs://wal_test_bucket/view/{item_id}.json",
            "item_id": item_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error retrieving task {item_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{item_id}/insights", response_model=TaskInsightResponse)
async def generate_task_insights(item_id: str, request: TaskInsightRequest):
    """Generate insights for a specific task"""
    try:
        # Get task data
        blob = bucket.blob(f"view/{item_id}.json")
        
        if not blob.exists():
            raise HTTPException(status_code=404, detail="Task not found")
            
        json_data = blob.download_as_text()
        task_data = json.loads(json_data)
        
        logger.info(f"🔍 Generating insights for task {item_id}")
        
        # Extract product information
        product_name = task_data.get("product_name", f"Task {item_id}")
        category = task_data.get("category", "Unknown Category")
        product_attributes = task_data.get("product_attributes", {})
        
        # Process attributes for insights
        site_summaries = []
        validation_summary = {
            "walmart_yes": 0,
            "walmart_no": 0, 
            "llm_yes": 0,
            "llm_no": 0,
            "total_attributes": len(product_attributes)
        }
        
        attribute_analysis = {}
        
        for attr_name, attr_data in product_attributes.items():
            if request.selected_attributes and attr_name not in request.selected_attributes:
                continue
                
            # Extract values from different sources
            walmart_value = attr_data.get("walmart_latest_value", "")
            llm_value = attr_data.get("llm_suggested_value", "")
            brand_value = attr_data.get("values_from_sources", {}).get("brand", "")
            
            # Get competitor values
            competitor_values = attr_data.get("competitor_values", {})
            selected_values = [v for v in [walmart_value, llm_value, brand_value] + list(competitor_values.values()) if v and v != "NULL" and v != "-"]
            
            # Get validation data
            validation_data = attr_data.get("validation_data", {})
            comment_data = attr_data.get("comment_data", {})
            selection_data = attr_data.get("selection_data", {})
            
            # Count validations
            walmart_validation = validation_data.get("walmart_validation", "").lower()
            llm_validation = validation_data.get("llm_validation", "").lower()
            
            if "yes" in walmart_validation:
                validation_summary["walmart_yes"] += 1
            elif "no" in walmart_validation:
                validation_summary["walmart_no"] += 1
                
            if "yes" in llm_validation:
                validation_summary["llm_yes"] += 1
            elif "no" in llm_validation:
                validation_summary["llm_no"] += 1
            
            # Create site summary for this attribute
            site_summary = SiteSummary(
                attribute=attr_name,
                selectedSources=["walmart", "llm", "brand"] + list(competitor_values.keys()),
                selectedValues=selected_values,
                walmartValidation=validation_data.get("walmart_validation", ""),
                llmValidation=validation_data.get("llm_validation", ""),
                walmartComment=comment_data.get("walmart_comment", ""),
                llmComment=comment_data.get("llm_comment", ""),
                finalVerdict=validation_data.get("final_verdict", "")
            )
            
            site_summaries.append(site_summary)
            
            # Store detailed analysis
            attribute_analysis[attr_name] = {
                "values": selected_values,
                "sources": len(competitor_values) + 3,  # walmart, llm, brand + competitors
                "final_value": selection_data.get("final_value", ""),
                "final_source": selection_data.get("source", ""),
                "validation_status": validation_data.get("final_validation", ""),
                "has_conflicts": len(set(selected_values)) > 1 if selected_values else False
            }
        
        # Generate insights using the existing function
        short_summary, long_summary, _ = generate_from_attributes(
            product_name=product_name,
            product_attributes={attr: analysis.get("final_value", "") for attr, analysis in attribute_analysis.items()},
            site_summaries=site_summaries
        )
        
        logger.info(f"✅ Generated insights for task {item_id}")
        
        return TaskInsightResponse(
            short_summary=short_summary,
            long_summary=long_summary,
            attribute_analysis=attribute_analysis if request.include_validations else None,
            validation_summary=validation_summary if request.include_validations else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error generating insights for task {item_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate insights: {str(e)}")

def generate_from_attributes(product_name: str, product_attributes: dict, site_summaries: list) -> tuple[str, str, str]:
    """Generate short summary, long summary, and about_this_item from product attributes and site summaries."""
    
    # Prepare input texts
    attributes_text = "\n".join([f"- {k}: {v}" for k, v in product_attributes.items()]) or "N/A"
    
    site_summaries_text = ""
    for s in site_summaries:
        values = ", ".join(s.selectedValues) if s.selectedValues else "N/A"
        sources = ", ".join(s.selectedSources) if s.selectedSources else "N/A"
        site_summaries_text += (
            f"Attribute: {s.attribute}\n"
            f"Values: {values}\n"
            f"Sources: {sources}\n"
            f"Walmart Validation: {s.walmartValidation or 'N/A'}\n"
            f"LLM Validation: {s.llmValidation or 'N/A'}\n"
            f"Final Verdict: {s.finalVerdict or 'N/A'}\n\n"
        )
    
    # Build prompt
    context = (
        f"Analyze this product: {product_name}\n\n"
        "Generate comprehensive insights based on the product attributes and validation data from multiple sources. "
        "Focus on key features, compatibility, quality indicators, and any notable patterns in the data validation."
    )
    
    prompt = f"""
{context}

Product Attributes:
{attributes_text}

Source Analysis:
{site_summaries_text}

Generate two summaries:

1. SHORT SUMMARY (2-3 sentences): Key highlights and main features
2. LONG SUMMARY (bullet points): Detailed breakdown of features, compatibility, specifications, and validation insights

Format your response exactly like this:
"BEST SHORT SUMMARY:\n"
        "<your short summary here, attributes naturally integrated>"
        "using the product attributes. "
        "The short summary should be concise (at least 55 words) highlighting key features and benefits. "
        "Explicitly explain what each attribute means for the customer (e.g., why the material matters, why dimensions are useful, "
        "why compatibility and vehicle fitment are important). "
        "Avoid technical jargon without explanation and make it engaging for buyers."
""RULES:  
1. The Short Description should be in paragraph form.  
2. Must be consistent with the actual product.  
3. Remove promotional text like warranty, free shipping, or discounts.  
4. Remove text that promotes another retailer.  
5. Keep ™, ®, ©. Remove unwanted symbols.  
6. Proper casing required.  
7. The Short Description should not repeat the Long Description.  
8. Remove duplicate text across Short and Long Descriptions.  
9. Short Description is for describing the product and brand/manufacturer story.  
10. If Short Description entirely in bullet form → move to Long Description and fail Short.  
11. If Short partially contains bullet-form features/specs → move those to Long Description.  
12. Short Description must clearly describe the product. If not, fail.  
13. Fix grammar and spelling errors.  
14. Leave HTML if present.  
15. Spell check is required.  
16. Verify dietary/nutritional claims.  
17. Minimum Word Count rule: if word count < minimum → fail.  

TASK INSTRUCTION:  
Given a product’s Short Description, Product Name, and Long Description:  

Clean and transform the Short Description following all rules above.  
If the Short Description fails the rules, output: "FAIL SHORT DESCRIPTION".  
If you move text to the Long Description, clearly mark it under "UPDATED LONG DESCRIPTION".  
Output must follow this format:  
""

BEST LONG SUMMARY:
TASK:  
Given a product’s Short Description, Product Name, and Long Description → clean and transform the Long Description following the rules below.  

OUTPUT FORMAT:  
- If valid, output must begin with:  
  BEST LONG SUMMARY:  
  • Property Name: description  
  • Property Name: description  

- If invalid → output exactly:  
  FAIL LONG DESCRIPTION  

- If text is moved to the Short Description, output it clearly under:  
  UPDATED SHORT DESCRIPTION: <new text>  

---

CONTENT RULES:  
-Strickly follow this
-do not include the word "Product Name":

1. The summary must contain bullet points.  
2. Each bullet must begin with: "• Property Name: description".  
3. Mandatory bullets (must always appear in the summary): 
   - **Product Name**: Exact match of the Product Name attribute.(do not include the word "Product Name:")
   - **Product Type**: Describe what the product is.  
   - **Application**: Specify the use or intended purpose of the product.  
   - **Utility**: Explain how the product is useful (factual, non-marketing).  
   - **Key Features**: Highlight the standout features of the product.  
   - Include exactly one bullet per mandatory attribute.
   - If an attribute value is missing, skip it. Do not invent placeholders.
   - Include these bullets at the start of the description, in the order listed above.
4. All passed attributes with valid values** (e.g., material, dimensions, weight, brand, compatibility, condition or any other PRODUCT ATTRIBUTES: ) must be included as separate bullets**. Do not skip any valid attribute.
5. Descriptions must remain factual only.  
   - No interpretation, persuasion, or explanations.  
   - No vague/repetitive wording (e.g., “ensures you receive,” “clearly indicates”).  
6. No second-person language (“you,” “your”).  
7. No marketing fluff. Tone must be technical and product-focused.  
8. Grammar, spelling, and casing must always be correct.  
9. If an attribute value is missing, skip it. Do not invent placeholders.  
10. If the format is not bullet points → return FAIL LONG DESCRIPTION.  
11. Content must be consistent with the actual product.  


---

STRUCTURAL RULES:  
1. If the Product Name is present in the Long Description in bold above the bullet points, leave it. If the Product Name differs, update it to match the Product Name attribute.  
2. Long Description must be in bullet point form. Paragraph-only descriptions → reject.  
3. Remove promotional text about discounts, shipping, warranties, or guarantees. Keep factual mentions of accessories, compatibility, or product variations.  
4. Remove external retailer mentions/redirects. Keep factual mentions like “Works with Amazon Alexa.”  
5. Symbols: keep ™, ®, ©. Remove €, †, ‡, •, ¢, £, ¥, ±, ¶, ~, â, ???.  
6. Ensure proper casing. ALL CAPS/lowercase is only allowed if validated Brand names or trademarks.  
7. Do not repeat the Short Description. Delete duplicate sentences across Short/Long descriptions.  
8. Brand/Manufacturer Story Handling:  
   - If in paragraph form (>2 sentences), move it to the end of the Short Description.  
   - If it is about features/specifications, keep in Long Description.  
9. If Long Description is a full paragraph and Short Description is empty → move it to Short Description and fail Long Description.  
10. If only part of the Long Description is a brand/manufacturer paragraph → move that part to Short Description.  
11. Must clearly describe the product. If unclear → fail Long Description.  
12. Correct grammatical/spelling errors.  
13. If HTML is present, leave it unchanged. Do not add/remove tags.  
14. If non-HTML bullet points are present, leave them; Ops team will reformat.  
15. Verify any nutritional/dietary claim against product images. Remove if not visible on packaging.  

---

EXAMPLES:  

. Bilstein B4 OE Replacement Strut Assembly(only product name should be there just name dont used product name)
• Material: Constructed from carbon steel for strength and durability.  
• Handle Design: Features a cushioned blue grip for comfort and skid resistance.  
• Ratchet Technology: Provides 360° swing for ease of positioning.  
• Application: Designed for disc brake systems with twin and quad piston calipers.  
• Usage: Suitable for both fixed and floating caliper systems.  
• Function: Expands and retracts spreading plates with simple handle movement.  
• Adjustment: Direction controlled by a reversible ratchet switch.  
• Compatibility: Works with single and twin-piston floating calipers.  
• Package Contents: Includes one brake piston compression tool.  
• Intended Use: Practical for professional mechanics and home garages.  

- ❌ Incorrect Output:  
"FAIL LONG DESCRIPTION" (when rules are not followed).  

"
"""
    
    # Call Gemini API
    try:
        model = genai.GenerativeModel("gemini-2.0-flash-exp")
        response = model.generate_content(prompt)
        result_text = response.text.strip()
    except Exception as e:
        logger.error(f"Gemini API error: {e}")
        return (
            f"Analysis completed for {product_name} with {len(product_attributes)} attributes evaluated.",
            f"• Product Analysis: Comprehensive review of {len(product_attributes)} product attributes\n• Data Sources: Multiple validation sources analyzed\n• Quality Assessment: Validation checks performed across all sources",
            None
        )
    
    # Parse output
    short_summary = "Analysis completed successfully."
    long_summary = "Detailed analysis has been performed."
    
    try:
        if "BEST SHORT SUMMARY:" in result_text:
            short_summary = result_text.split("BEST SHORT SUMMARY:")[1].split("BEST LONG SUMMARY:")[0].strip()
    except:
        pass
        
    try:
        if "BEST LONG SUMMARY:" in result_text:
            long_summary = result_text.split("BEST LONG SUMMARY:")[1].strip()
    except:
        pass
    
    return short_summary, long_summary, None
async def update_master_summary_sheet(req: SaveTaskDataRequest) -> Dict[str, Any]:
    """
    Updates the master summary sheet with task overview data and actual attribute data values
    in wide format (similar to save_task_data_to_sheets) but without brand and competitor data.
    Ensures headers are always present in row 1.
    """
    try:
        # Init Sheets API
        sheets_credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE,
            scopes=['https://www.googleapis.com/auth/spreadsheets']
        )
        service = build('sheets', 'v4', credentials=sheets_credentials)
        sheet = service.spreadsheets()

        summary_sheet_name = "Master_Summary"

        # Ensure sheet exists
        spreadsheet = sheet.get(spreadsheetId=SPREADSHEET_ID).execute()
        existing_sheets = [s['properties']['title'] for s in spreadsheet['sheets']]

        if summary_sheet_name not in existing_sheets:
            sheet.batchUpdate(
                spreadsheetId=SPREADSHEET_ID,
                body={
                    "requests": [{
                        "addSheet": {
                            "properties": {
                                "title": summary_sheet_name,
                                "gridProperties": {
                                    "rowCount": 2000,
                                    "columnCount": 200  # wide format capacity
                                }
                            }
                        }
                    }]
                }
            ).execute()
            logger.info(f"✅ Created summary sheet: {summary_sheet_name}")

        # ------------------------------
        # ✅ Build wide headers (NO brand, NO competitor cols)
        # ------------------------------
        base_headers = [
            "Task ID", "Product Name", "Category", "Total Attributes",
            "Selected Cells", "Has Insights", "Saved At", "Sheet Name",
            "Total Competitors", "Request ID"
        ]

        attribute_headers = []
        for row in req.table_data:
            attr = row.attribute.replace(" ", "_").replace("|", "")
            attr_cols = [
                f"{attr}_Walmart",
                f"{attr}_LLM",
                f"{attr}_Final_Value",
                f"{attr}_Final_Comment",
                f"{attr}_Final_Verdict",
                f"{attr}_Final_Source"
            ]
            attribute_headers.extend(attr_cols)

        headers = base_headers + attribute_headers

        # ------------------------------
        # ✅ Build summary row
        # ------------------------------
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        all_competitors = set()
        for row in req.table_data:
            all_competitors.update(row.competitor_values.keys())

        summary_row = [
            req.item_id,
            req.product_name or "Unknown Product",
            req.category or "Unknown Category",
            len(req.table_data),
            len(req.selected_cells),
            "Yes" if req.insights_data else "No",
            current_time,
            f"Task_{req.item_id}",
            len(all_competitors),
            f"save_{int(time.time() * 1000)}"
        ]

        # Add attribute data (exclude brand + competitors)
        for row in req.table_data:
            summary_row.extend([
                row.walmart or "",
                row.llm or "",
                row.final_value or "",
                " | ".join([f"{k}: {v}" for k, v in row.comment_data.items() if v]) or "",
                row.final_verdict or "",
                row.final_source or ""
            ])

        # ------------------------------
        # ✅ Ensure headers in row 1
        # ------------------------------
        existing_data = sheet.values().get(
            spreadsheetId=SPREADSHEET_ID,
            range=f"{summary_sheet_name}!1:1"   # first row
        ).execute()

        first_row = existing_data.get("values", [])

        # If no headers or outdated headers → overwrite row 1
        if not first_row or len(first_row[0]) < len(headers):
            sheet.values().update(
                spreadsheetId=SPREADSHEET_ID,
                range=f"{summary_sheet_name}!A1",
                valueInputOption='RAW',
                body={'values': [headers]}
            ).execute()
            logger.info(f"📝 Headers written/updated in {summary_sheet_name}")

        # ------------------------------
        # ✅ Find next empty row & insert data
        # ------------------------------
        all_rows = sheet.values().get(
            spreadsheetId=SPREADSHEET_ID,
            range=f"{summary_sheet_name}!A:A"
        ).execute()

        existing_rows = len(all_rows.get('values', []))
        next_row = existing_rows + 1

        result = sheet.values().update(
            spreadsheetId=SPREADSHEET_ID,
            range=f"{summary_sheet_name}!A{next_row}",
            valueInputOption='RAW',
            body={'values': [summary_row]}
        ).execute()

        logger.info(f"✅ Added row {next_row} in {summary_sheet_name} with wide format (no brand/competitors)")

        return {
            "status": "success",
            "row_added": next_row,
            "cells_updated": result.get('updatedCells', 0),
            "columns_used": len(summary_row),
            "total_headers": len(headers),
            "attributes_saved": len(req.table_data),
            "brand_included": False,
            "competitors_included": False
        }

    except Exception as e:
        logger.error(f"❌ update_master_summary_sheet failed: {e}")
        return {"status": "error", "message": str(e)}



async def save_task_data_to_sheets(req: SaveTaskDataRequest, request_id: str) -> Dict[str, Any]:
    """Save task data in WIDE FORMAT - each attribute becomes multiple columns"""
    try:
        # Initialize Google Sheets API
        sheets_credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE,
            scopes=['https://www.googleapis.com/auth/spreadsheets']
        )
        service = build('sheets', 'v4', credentials=sheets_credentials)
        sheet = service.spreadsheets()
        
        sheet_name = "QAView_SummaryFull"
        
        # Delete existing sheet if it exists
        try:
            spreadsheet = sheet.get(spreadsheetId=SPREADSHEET_ID).execute()
            existing_sheets = [s['properties']['title'] for s in spreadsheet['sheets']]
            
            if sheet_name in existing_sheets:
                sheet_id = None
                for s in spreadsheet['sheets']:
                    if s['properties']['title'] == sheet_name:
                        sheet_id = s['properties']['sheetId']
                        break
                
                if sheet_id is not None:
                    sheet.batchUpdate(
                        spreadsheetId=SPREADSHEET_ID,
                        body={"requests": [{"deleteSheet": {"sheetId": sheet_id}}]}
                    ).execute()
                    logger.info(f"🗑️ Deleted existing sheet: {sheet_name}")
        except Exception as e:
            logger.warning(f"Warning checking existing sheets: {e}")

        # ✅ WIDE FORMAT: Build headers and data row
        # Start with basic headers
        headers = [
            'Submission_ID', 'Timestamp', 'Feedback', 'Short_Summary', 'Detailed_Summary', 
            'Attributes_Count', 'Walmart URL', 'Product Type', 'Category', 'Product ID', 
            'Item ID', 'Final LLM Link'
        ]
        
        # Get all unique competitors across all attributes
        all_competitors = set()
        for row in req.table_data:
            all_competitors.update(row.competitor_values.keys())
        all_competitors = sorted(list(all_competitors))
        
        # ✅ For each attribute, create multiple columns with all data points
        attribute_columns = []
        for row in req.table_data:
            attr_name = row.attribute.replace(" ", " ").replace("|", "|")  # Keep original naming
            
            # Add all columns for this attribute
            attr_cols = [
                f"{attr_name} Initial prefilled values",
                f"{attr_name} LLM Suggested value", 
                f"{attr_name} LLM_suggested_url",
                f"{attr_name} Walmart_Latest values",
                f"{attr_name}_Brand_value",
                f"{attr_name}_Final_Choice",
                f"{attr_name}_Final_Comment", 
                f"{attr_name}_Final_Source",
                f"{attr_name}_Final_Validation",
                f"{attr_name}_Final_Verdict",
                f"{attr_name}_Final_Verdict_Combined",
                f"{attr_name}_LLM_Comment",
                f"{attr_name}_LLM_Validate", 
                f"{attr_name}_LLM_Validation",
                f"{attr_name}_LLM_value",
                f"{attr_name}_Selected_Sources",
                f"{attr_name}_WalmartLatest_Comment",
                f"{attr_name}_WalmartLatest_Validation",
                f"{attr_name}_WalmartLatest_value",
                f"{attr_name}_Walmart_Comment",
                f"{attr_name}_Walmart_Validated",
                f"{attr_name}_Walmart_value"
            ]
            
            # Add competitor columns for this attribute
            for comp in all_competitors:
                attr_cols.append(f"{attr_name}_{comp}_value")
            
            attribute_columns.extend(attr_cols)

        # Add summary columns
        summary_columns = [
            'Competitor_Names', 'Final_Verdicts_Count', 'LLM_No_Count', 'LLM_Yes_Count',
            'WalmartLatest_No_Count', 'WalmartLatest_Yes_Count', 'Walmart_Latest URL',
            'Walmart_No_Count', 'Walmart_Yes_Count', 'Total_Attributes', 
            'Total_Competitors', 'Total_Selections', 'user'
        ]
        
        # Add competitor source URLs
        for comp in all_competitors:
            summary_columns.append(f"{comp} Source URL")
        
        # Combine all headers
        headers.extend(attribute_columns)
        headers.extend(summary_columns)

        # ✅ Build the data row
        data_row = [
            req.item_id,  # Submission_ID
            time.strftime("%Y-%m-%d %H:%M:%S"),  # Timestamp
            req.insights_data.get("feedback", ""),  # Feedback
            req.insights_data.get("short_summary", ""),  # Short_Summary
            req.insights_data.get("detailed_summary", ""),  # Detailed_Summary
            len(req.table_data),  # Attributes_Count
            "",  # Walmart URL - you can populate from task data
            req.category or "",  # Product Type
            req.category or "",  # Category
            "",  # Product ID - you can populate from task data
            req.item_id,  # Item ID
            "",  # Final LLM Link - you can populate from task data
        ]
        
        # Add attribute data
        for row in req.table_data:
            # Basic attribute data
            attr_data = [
                "",  # Initial prefilled values
                row.llm,  # LLM Suggested value
                "",  # LLM_suggested_url
                row.walmart,  # Walmart_Latest values
                row.brand,  # Brand_value
                row.final_value,  # Final_Choice
                "",  # Final_Comment
                row.final_source,  # Final_Source
                "",  # Final_Validation
                row.final_verdict,  # Final_Verdict
                "",  # Final_Verdict_Combined
                row.comment_data.get("llm_comment", ""),  # LLM_Comment
                "",  # LLM_Validate
                row.validation_data.get("llm_validation", ""),  # LLM_Validation
                row.llm,  # LLM_value
                "",  # Selected_Sources
                row.comment_data.get("walmart_latest_comment", ""),  # WalmartLatest_Comment
                row.validation_data.get("walmart_latest_validation", ""),  # WalmartLatest_Validation
                row.walmart,  # WalmartLatest_value
                row.comment_data.get("walmart_comment", ""),  # Walmart_Comment
                "",  # Walmart_Validated
                row.walmart  # Walmart_value
            ]
            
            # Add competitor values for this attribute
            for comp in all_competitors:
                attr_data.append(row.competitor_values.get(comp, ""))
            
            data_row.extend(attr_data)

        # Add summary data
        summary_data = [
            ", ".join(all_competitors),  # Competitor_Names
            sum(1 for row in req.table_data if row.final_verdict),  # Final_Verdicts_Count
            sum(1 for row in req.table_data if row.validation_data.get("llm_validation", "").lower() == "no"),  # LLM_No_Count
            sum(1 for row in req.table_data if row.validation_data.get("llm_validation", "").lower() == "yes"),  # LLM_Yes_Count
            sum(1 for row in req.table_data if row.validation_data.get("walmart_latest_validation", "").lower() == "no"),  # WalmartLatest_No_Count
            sum(1 for row in req.table_data if row.validation_data.get("walmart_latest_validation", "").lower() == "yes"),  # WalmartLatest_Yes_Count
            "",  # Walmart_Latest URL
            sum(1 for row in req.table_data if row.validation_data.get("walmart_validation", "").lower() == "no"),  # Walmart_No_Count
            sum(1 for row in req.table_data if row.validation_data.get("walmart_validation", "").lower() == "yes"),  # Walmart_Yes_Count
            len(req.table_data),  # Total_Attributes
            len(all_competitors),  # Total_Competitors
            len(req.selected_cells),  # Total_Selections
            "user1"  # user
        ]
        
        # Add competitor source URLs (empty for now)
        for comp in all_competitors:
            summary_data.append("")  # Competitor Source URL
        
        data_row.extend(summary_data)

        # ✅ Create the final data structure
        all_rows = [headers, data_row]  # Just header row and one data row

        # Create new sheet
        try:
            sheet.batchUpdate(
                spreadsheetId=SPREADSHEET_ID,
                body={
                    "requests": [{
                        "addSheet": {
                            "properties": {
                                "title": sheet_name,
                                "gridProperties": {
                                    "rowCount": 10,
                                    "columnCount": len(headers) + 10
                                }
                            }
                        }
                    }]
                }
            ).execute()
            
            logger.info(f"✅ Created new sheet: {sheet_name}")
            
        except Exception as sheet_error:
            if "already exists" in str(sheet_error):
                logger.warning(f"Sheet {sheet_name} already exists, using existing sheet")
            else:
                logger.error(f"Error creating sheet: {sheet_error}")
                raise

        # Write data to the sheet
        range_name = f"{sheet_name}!A1"
        
        result = sheet.values().update(
            spreadsheetId=SPREADSHEET_ID,
            range=range_name,
            valueInputOption='RAW',
            body={'values': all_rows}
        ).execute()

        logger.info(f"✅ Wide format data written: {result.get('updatedCells', 0)} cells updated")
        
        return {
            "success": True,
            "sheet_name": sheet_name,
            "rows_written": len(all_rows),
            "spreadsheet_url": f"https://docs.google.com/spreadsheets/d/{SPREADSHEET_ID}/edit#gid=0",
            "range": range_name,
            "insights_included": bool(req.insights_data),
            "competitors_included": len(all_competitors),
            "cells_updated": result.get('updatedCells', 0),
            "format": "wide_format",
            "total_columns": len(headers),
            "task_id": req.item_id
        }
        
    except Exception as e:
        logger.error(f"Google Sheets wide format save failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to save to Google Sheets in wide format"
        }


@router.post("/{item_id}/save", response_model=SaveTaskDataResponse)
async def save_task_data(item_id: str, req: SaveTaskDataRequest):
    """Save task table data and insights to Google Sheets - ONE file, multiple sheets"""
    request_id = f"save_{int(time.time() * 1000)}"
    logger.info(f"💾 SAVE TASK DATA ENDPOINT [{request_id}] for item_id: {item_id}")
    logger.info(f"📊 Table rows: {len(req.table_data)}")
    logger.info(f"🎯 Selected cells: {len(req.selected_cells)}")
    logger.info(f"🧠 Has insights: {bool(req.insights_data)}")

    try:
        sheets_result = {}
        
        if req.save_to_sheets:
            logger.info("📤 Processing Google Sheets save...")
            sheets_result = await save_task_data_to_sheets(req, request_id)
            
            # ✅ OPTIONAL: Update master summary
            if sheets_result.get("success"):
                try:
                    master_summary_result = await update_master_summary_sheet(req)
                    logger.info(f"📊 Master summary result: {master_summary_result}")
                except Exception as summary_error:
                    logger.warning(f"⚠️ Master summary update failed (non-critical): {summary_error}")
        else:
            logger.warning("⚠️ No save option selected")
            return SaveTaskDataResponse(
                success=False,
                message="❌ No save option selected",
                sheets_result={},
                timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
                total_rows_saved=0
            )
        
        total_rows_saved = 0
        if sheets_result.get("success"):
            total_rows_saved = sheets_result.get("rows_written", 0)
        
        success_message = f"✅ Task {item_id} saved to Google Sheets" if sheets_result.get("success") else "❌ Failed to save to Google Sheets"
        
        response = SaveTaskDataResponse(
            success=sheets_result.get("success", False),
            message=success_message,
            sheets_result=sheets_result,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            total_rows_saved=total_rows_saved
        )
        
        logger.info(f"✅ [{request_id}] Task data save completed")
        logger.info(f"📊 Sheets success: {sheets_result.get('success', False)}")
        
        return response
        
    except Exception as e:
        logger.error(f"❌ [{request_id}] Save task data failed: {e}")
        raise HTTPException(status_code=500, detail=f"Error saving task data: {e}")

@router.get("/")
async def list_tasks():
    """List all tasks in the bucket"""
    try:
        blobs = bucket.list_blobs(prefix="view/")
        tasks = []
        
        for blob in blobs:
            if blob.name.endswith('.json'):
                item_id = blob.name.replace('view/', '').replace('.json', '')
                
                try:
                    partial_data = blob.download_as_text(start=0, end=min(1000, blob.size))
                    if partial_data:
                        partial_json = json.loads(partial_data)
                        product_name = partial_json.get("product_name", "Unknown Product")[:50]
                    else:
                        product_name = "Unknown Product"
                except:
                    product_name = "Unknown Product"
                
                tasks.append({
                    "item_id": item_id,
                    "product_name": product_name,
                    "blob_path": blob.name,
                    "full_path": f"gs://wal_test_bucket/{blob.name}",
                    "created": blob.time_created.isoformat() if blob.time_created else None,
                    "updated": blob.updated.isoformat() if blob.updated else None,
                    "size": blob.size,
                    "view_url": f"/task/{item_id}/view"
                })
        
        tasks.sort(key=lambda x: x.get("updated", ""), reverse=True)
        
        logger.info(f"📋 Found {len(tasks)} tasks in gs://wal_test_bucket/view/")
        
        return {
            "success": True,
            "bucket_name": "wal_test_bucket",
            "prefix": "view/",
            "total_tasks": len(tasks),
            "tasks": tasks
        }
        
    except Exception as e:
        logger.error(f"❌ Error listing tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
