import logging
import re
import uuid
from typing import Any, Dict, List

import numpy as np
import pandas as pd
from fastapi import APIRouter, UploadFile, File, HTTPException
from google.cloud import bigquery
from pydantic import BaseModel
from io import StringIO

from database import bq_client
from config import (
    get_bq_client, 
    get_gcs_client, 
    PROJECT_ID, 
    DATASET_ID, 
    ASSIGN_TABLE_FULL,
    USERS_TABLE
)

router = APIRouter()

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# --- BigQuery table ---
ASSIGN_TABLE = ASSIGN_TABLE_FULL

# --- Schema columns (normalized) ---
SCHEMA_COLUMNS = [
  "date_yyyy_mm_dd", "work_type", "associate_walmart_id", "item_status",
  "submission_id", "product_id_type", "product_id", "item_id",
  "audit_template_version", "initial_cq_score", "supplier_id",
  "item_created_date", "active_status", "website_link",
  "view_images", "category", "product_type_group", "product_type",
  "product_name", "brand", "main_image_url",
  "assign", "status", "assigned_at"
]



# --- Utilities ---
def normalize_col(name: str) -> str:
    name = name.strip().lower()
    name = re.sub(r"[^\w]+", "_", name)   # replace spaces, (), ?, &, etc.
    name = re.sub(r"_+", "_", name)
    return name.strip("_")

def clean_value(val):
    """Convert NaN/NaT/numpy types into JSON-safe Python types"""
    if pd.isna(val):
        return None
    if isinstance(val, (np.generic,)):
        return val.item()
    return val


# ===============================
# 📌 UPLOAD ENDPOINT
# ===============================
@router.post("/upload")
async def upload_assign(file: UploadFile = File(...)):
    logging.info(f"📡 Upload request received - filename={file.filename}")

    try:
        contents = await file.read()
        df_original = pd.read_csv(StringIO(contents.decode("utf-8")))

        # ✅ Keep original columns for processing
        original_columns = df_original.columns.tolist()
        logging.info(f"📋 Original CSV has {len(original_columns)} columns")
        
        # ✅ Create normalized version for BigQuery
        df_normalized = df_original.copy()
        df_normalized.columns = [normalize_col(c) for c in df_normalized.columns]

        # ✅ Filter for BigQuery schema (24 columns)
        df_filtered = df_normalized[[col for col in SCHEMA_COLUMNS if col in df_normalized.columns]].copy()

        # ✅ Ensure required columns for BigQuery
        if "status" not in df_filtered.columns:
            df_filtered["status"] = "Pending"
        else:
            df_filtered["status"] = df_filtered["status"].fillna("Pending")

        df_filtered["assign"] = None  # Always unassigned at upload

        # ✅ Handle duplicates in both datasets
        if "item_id" in df_filtered.columns:
            before = len(df_filtered)
            duplicate_mask = df_filtered.duplicated(subset=["item_id"], keep='first')
            df_filtered = df_filtered[~duplicate_mask]
            df_original = df_original[~duplicate_mask]  # Apply same filter to original
            after = len(df_filtered)
            logging.info(f"🧹 Removed {before - after} duplicate rows")
        else:
            before, after = len(df_filtered), len(df_filtered)

        # ✅ Clean data
        df_filtered = df_filtered.where(pd.notnull(df_filtered), None)
        df_original = df_original.where(pd.notnull(df_original), None)

        # ✅ Convert to records
        rows_preview = []
        for rec in df_filtered.to_dict(orient="records"):
            clean_rec = {k: clean_value(v) for k, v in rec.items()}
            rows_preview.append(clean_rec)

        rows_full = []
        for rec in df_original.to_dict(orient="records"):
            clean_rec = {k: clean_value(v) for k, v in rec.items()}
            rows_full.append(clean_rec)

        if not rows_preview:
            raise HTTPException(status_code=400, detail="No valid rows found in CSV")

        return {
            "message": "✅ File uploaded, ready to save",
            "removed_duplicates": before - after,
            "saved": 0,
            "columns": list(df_filtered.columns),        # BigQuery columns (24)
            "original_columns": original_columns,        # Full CSV columns (140+)
            "preview": rows_preview[:50],                # BigQuery data (24 cols)
            "full_data": rows_full,                      # ✅ Full data (ALL columns)
            "total_rows": len(rows_full)
        }

    except Exception as e:
        logging.error(f"❌ Error uploading assign data: {e}")
        raise HTTPException(status_code=500, detail=str(e))



# ===============================
# 📌 SAVE ENDPOINT
# ===============================

class SaveRequest(BaseModel):
    rows: List[Dict[str, Any]]

@router.post("/save")
async def save_assign(data: SaveRequest):
    try:
        if not data.rows:
            raise HTTPException(status_code=400, detail="No rows to save")

        # ✅ Collect valid integer item_ids
        incoming_item_ids = []
        for r in data.rows:
            try:
                if r.get("item_id") is not None:
                    incoming_item_ids.append(int(r["item_id"]))
            except ValueError:
                logging.warning(f"Skipping invalid item_id: {r.get('item_id')}")

        if not incoming_item_ids:
            raise HTTPException(status_code=400, detail="No valid item_id values in rows")

        # ✅ Query existing item_ids in BigQuery
        query = f"""
        SELECT DISTINCT item_id
        FROM `{ASSIGN_TABLE}`
        WHERE item_id IN UNNEST(@item_ids)
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[bigquery.ArrayQueryParameter("item_ids", "INT64", incoming_item_ids)]
        )
        existing_ids = {int(row.item_id) for row in bq_client.query(query, job_config=job_config).result()}
        logging.info(f"🔎 Found {len(existing_ids)} existing item_ids in BigQuery")

        # ✅ Prepare rows for insert
        rows_to_insert = []
        for r in data.rows:
            try:
                item_id = int(r.get("item_id"))
            except (TypeError, ValueError):
                continue
            if item_id in existing_ids:
                continue

            # --- Only include allowed schema columns ---
            clean_rec = {
                k: clean_value(v)
                for k, v in r.items()
                if k in SCHEMA_COLUMNS
            }

            # --- Force defaults to avoid streaming buffer updates ---
            clean_rec["status"] = "Pending"          # Always Pending
            clean_rec["assign"] = None               # Always unassigned
            clean_rec["assigned_at"] = None          # Always null
            clean_rec["id_str"] = str(uuid.uuid4())  # Unique ID

            rows_to_insert.append(clean_rec)

        if not rows_to_insert:
            return {
                "message": "⚠️ No new rows to insert (all duplicates)",
                "saved": 0,
                "skipped": len(incoming_item_ids)
            }

        # ✅ Insert into BigQuery
        errors = bq_client.insert_rows_json(ASSIGN_TABLE, rows_to_insert)
        if errors:
            logging.error(f"BigQuery insert error: {errors}")
            raise HTTPException(status_code=500, detail=f"BigQuery insert error: {errors}")

        return {
            "message": "✅ Data saved successfully",
            "saved": len(rows_to_insert),
            "skipped": len(incoming_item_ids) - len(rows_to_insert),
        }

    except Exception as e:
        logging.error(f"❌ Error saving assign data: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ===============================
# 📌 USER LIST 
# ===============================
@router.get("/users/list")
def list_users():
    try:
        query = f"""
        SELECT username 
        FROM `{PROJECT_ID}.{DATASET_ID}.{USERS_TABLE}`
        WHERE role = 'user'
        """
        logging.info(f"📡 Running query: {query}")

        rows = list(bq_client.query(query).result())
        logging.info(f"✅ Users fetched: {len(rows)} rows")
        for r in rows:
            logging.info(f"👤 User row: {dict(r)}")

        return {"users": [dict(r) for r in rows]}
    except Exception as e:
        logging.error(f"❌ Error fetching users: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tasks/claim")
def claim_task(username: str):
    """
    Atomically assign the oldest unassigned task (first-come, first-served)
    to the given user.
    """
    try:
        logging.info(f"📡 Task claim request received for username={username}")

        # Step 1: Find next available task
        find_query = f"""
        SELECT *
        FROM `{ASSIGN_TABLE}`
        WHERE assign IS NULL OR assign = "Unassigned"
        ORDER BY date_yyyy_mm_dd ASC
        LIMIT 1
        """
        logging.info(f"🔎 Running find_query:\n{find_query}")

        task_rows = list(bq_client.query(find_query).result())
        logging.info(f"📊 Found {len(task_rows)} candidate tasks")

        if not task_rows:
            logging.warning("⚠️ No unassigned tasks available to claim")
            return {"message": "⚠️ No tasks available"}

        task = dict(task_rows[0])
        task_id = task["id_str"]
        logging.info(f"✅ Selected task id_str={task_id} for username={username}")

        # Step 2: Update the task
        update_query = f"""
        UPDATE `{ASSIGN_TABLE}`
        SET assign = @username
        WHERE id_str = @task_id
        """
        logging.info(f"✏️ Running update_query for task_id={task_id}")

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("username", "STRING", username),
                bigquery.ScalarQueryParameter("task_id", "STRING", task_id),
            ]
        )
        bq_client.query(update_query, job_config=job_config).result()
        logging.info(f"📝 Task {task_id} successfully assigned to {username}")

        # Step 3: Return updated task
        task["assign"] = username
        logging.info(f"🚀 Returning claimed task: {task}")

        return {"message": "✅ Task claimed", "task": task}

    except Exception as e:
        logging.error(f"❌ Error claiming task for user={username}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


class AssignRequest(BaseModel):
    username: str
    item_id: int

@router.post("/tasks/assign")
def assign_task(data: AssignRequest):
    """
    Directly assign a specific task (by item_id) to a given user.
    """
    try:
        logging.info(f"📡 Assign request: item_id={data.item_id}, username={data.username}")

        # Step 1: Ensure task exists
        check_query = f"""
        SELECT id_str, assign
        FROM `{ASSIGN_TABLE}`
        WHERE item_id = @item_id
        LIMIT 1
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[bigquery.ScalarQueryParameter("item_id", "INT64", data.item_id)]
        )
        rows = list(bq_client.query(check_query, job_config=job_config).result())

        if not rows:
            logging.warning(f"⚠️ No task found with item_id={data.item_id}")
            raise HTTPException(status_code=404, detail="Task not found")

        task = dict(rows[0])
        task_id = task["id_str"]

        # Step 2: Update assignment
        update_query = f"""
        UPDATE `{ASSIGN_TABLE}`
        SET assign = @username, assigned_at = CURRENT_TIMESTAMP()
        WHERE id_str = @task_id
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("username", "STRING", data.username),
                bigquery.ScalarQueryParameter("task_id", "STRING", task_id),
            ]
        )
        bq_client.query(update_query, job_config=job_config).result()

        logging.info(f"✅ Task {task_id} (item_id={data.item_id}) assigned to {data.username}")

        return {"message": f"Task {data.item_id} successfully assigned to {data.username}"}

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"❌ Error assigning task: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
