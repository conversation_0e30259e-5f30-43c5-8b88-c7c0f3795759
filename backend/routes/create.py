import pandas as pd
import json

GOOGLE_SHEET_URL = "https://docs.google.com/spreadsheets/d/1OI_0AbuKpS6RN6rMVXW3ig9v9dPdjDzY/export?format=xlsx&id=1OI_0AbuKpS6RN6rMVXW3ig9v9dPdjDzY&gid=1463338633"

# -------------------------------
# Load sheet into DataFrame
# -------------------------------
df = pd.read_excel(GOOGLE_SHEET_URL, sheet_name="AttributeLevelData")  # update if sheet name differs
df.columns = df.columns.str.strip()

# -------------------------------
# Build JSON structure
# -------------------------------
output = {}

def add_attribute(domain, category, attribute_obj):
    """Helper: Add attribute if not already present"""
    if domain not in output:
        output[domain] = {}
    if category not in output[domain]:
        output[domain][category] = {"attributes": []}

    existing_names = [a["attribute_name"] for a in output[domain][category]["attributes"]]
    if attribute_obj["attribute_name"] not in existing_names:
        output[domain][category]["attributes"].append(attribute_obj)

# -------------------------------
# Predefine Brand_name attribute
# -------------------------------
brand_attribute = {
    "attribute_name": "Brand",
    "display_name": "Brand ",
    "requirement_level": "required",
    "conditional_requirement": "",
    "ops_tooling": "",
    "validation_instructions": "Always extract brand name",
    "definition": "The official brand name of the product",
    "examples": "",
    "is_closed_list": "false",
    "acceptable_attributes": {"values": [], "units": []},
    "is_multi_select": "false",
    "data_type": "string",
    "min_char_length": 1,
    "max_char_length": 100,
    "min_word": "",
    "recommended_words": "",
    "min_entries": "",
    "recommended_entries": "",
    "precision": 0
}

# -------------------------------
# Process spreadsheet rows
# -------------------------------
for _, row in df.iterrows():
    domain = str(row["Category"]).strip()      # e.g., Vehicle
    category = str(row["Data Model"]).strip()  # e.g., Resonators

    # Handle acceptable values & units
    acceptable_values = str(row.get("Acceptable Values", "")).strip()
    acceptable_units = str(row.get("Acceptable Units", "")).strip()
    
    ops_tooling = str(row.get("OPS Tooling", "")).strip().lower()
    print(ops_tooling)
    if ops_tooling == "do not validate":
        continue
    acceptable_dict = {
        "values": [v.strip() for v in acceptable_values.replace(";", ",").split(",") if v.strip()],
        "units": [u.strip() for u in acceptable_units.replace(";", ",").split(",") if u.strip()]
    }
    if "ops_tooling": str(row.get("OPS Tooling", "")).strip(),
    attribute_obj = {
        "attribute_name": str(row.get("Attribute Name", "")).strip(),
        "display_name": str(row.get("Display Name", "")).strip(),
        "requirement_level": str(row.get("Requirement Level", "")).strip(),
        "conditional_requirement": str(row.get("Conditional Requirement", "")).strip(),
        "ops_tooling": str(row.get("OPS Tooling", "")).strip(),
        "validation_instructions": str(row.get("Validation Instructions", "")).strip(),
        "definition": str(row.get("Definition", "")).strip(),
        "examples": str(row.get("Examples", "")).strip(),
        "is_closed_list": str(row.get("Is Closed List", "")).strip().lower(),
        "acceptable_attributes": acceptable_dict,
        "is_multi_select": str(row.get("Is Multi-Select", "")).strip().lower(),
        "data_type": str(row.get("Data Type", "")).strip(),
        "min_char_length": int(row.get("Min Char Length", 0) or 0),
        "max_char_length": int(row.get("Max Char Length", 0) or 0),
        "min_word": str(row.get("Min Word", "")).strip(),
        "recommended_words": str(row.get("Recommended Words", "")).strip(),
        "min_entries": str(row.get("Min Entries", "")).strip(),
        "recommended_entries": str(row.get("Recommended Entries", "")).strip(),
        "precision": int(row.get("Precision", 0) or 0),
    }

    if category.lower() == "all":
        # Copy into all real categories
        all_categories = set(df[df["Category"] == domain]["Data Model"].dropna().unique())
        for cat in all_categories:
            if str(cat).strip().lower() != "all":
                add_attribute(domain, cat, attribute_obj)
                add_attribute(domain, cat, brand_attribute)  # Add Brand_name
    else:
        add_attribute(domain, category, attribute_obj)
        add_attribute(domain, category, brand_attribute)  # Add Brand_name

# -------------------------------
# Save JSON
# -------------------------------
with open("output.json", "w", encoding="utf-8") as f:
    json.dump(output, f, indent=4, ensure_ascii=False)

print("✅ JSON file created: output.json (Brand_name add_)")