import http.client
import requests
from bs4 import BeautifulSoup
import google.generativeai as genai
import pandas as pd
from google.cloud import storage
from urllib.parse import urlparse
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from requests.exceptions import HTTPError, RequestException
import time
from urllib.parse import urlparse
import re
from google.cloud import storage, bigquery
from google.oauth2 import service_account
import json
from pydantic import BaseModel
from typing import Optional
from typing import List, Dict, Any
import hashlib
import logging
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
from datetime import datetime
from fastapi import FastAPI, HTTPException, Request
from google.api_core.exceptions import PreconditionFailed
from fastapi import APIRouter
from config import (
    get_bq_client, 
    get_gcs_client, 
    PROJECT_ID, 
    DATASET_ID, 
    ASSIGN_TABLE_FULL,
    BUCKET_NAME,
    THREAD_FOLDER,
    GEMINI_API_KEY
)

# Simple client initialization
gcs_client = get_gcs_client()
bq_client = get_bq_client()
router = APIRouter()

# ✅ Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ✅ Configuration constants
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
NUM_THREADS = 5

if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)  # ✅ Uses config value
    logger.info("✅ Gemini API configured successfully")
else:
    logger.error("❌ GEMINI_API_KEY not available from config")


# ✅ Pydantic models for request validation


class ClaimRequest(BaseModel):
    user_id: str

# ✅ Main API endpoint


@router.post("/button")
async def claim_endpoint(req: ClaimRequest):
    """
    Claim next available task for a user
    """
    user_id = req.user_id
    if not user_id:
        raise HTTPException(status_code=400, detail="user_id required")

    logger.info(f"Claim task request from user: {user_id}")

    try:
        result = claim_task_and_update_bq(
            user_id, dataset_table="walmart.details")

        if not result:
            logger.info(f"No pending tasks available for user: {user_id}")
            return {
                "status": "empty",
                "message": "No pending tasks available"
            }

        logger.info(
            f"Task claimed successfully for user {user_id}: {result['url']}")
        return result

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Claim API error for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Claim API error: {str(e)}")

# ✅ Core claim logic


def claim_task_and_update_bq(user_id: str, dataset_table: str = "walmart.details"):
    """
    Main function to claim a task and update BigQuery
    """
    # Get list of thread files
    thread_files = list_thread_files()
    if not thread_files:
        logger.warning("No thread files found in GCS")
        return None

    # Randomize to distribute load
    random.shuffle(thread_files)
    logger.info(f"Found {len(thread_files)} thread files to check")

    for tf in thread_files:
        logger.debug(f"Checking thread file: {tf}")

        # Try to claim a task from this thread file
        claim = try_claim_in_blob(tf, user_id)
        if not claim:
            continue

        url = claim.get("url")
        thread_file = claim.get("thread_file")

        logger.info(f"Claimed URL from blob: {url}")

        try:
            # Check if URL exists in BigQuery
            exists = bq_url_exists(dataset_table, url)
        except Exception as e:
            logger.error(f"BigQuery existence check failed for {url}: {e}")
            revert_claim_in_blob(thread_file, user_id, url)
            raise HTTPException(
                status_code=500, detail=f"BigQuery check error: {e}")

        if not exists:
            logger.warning(f"URL not found in BigQuery: {url}")
            revert_claim_in_blob(thread_file, user_id, url)
            raise HTTPException(
                status_code=404, detail=f"URL not found in BigQuery: {url}")

        try:
            # Update BigQuery with assignment
            mark_url_assigned_in_bq(dataset_table, url, user_id)
            logger.info(
                f"Updated BigQuery assignment for {url} to user {user_id}")
        except Exception as e:
            logger.error(f"Failed to update BigQuery for {url}: {e}")
            revert_claim_in_blob(thread_file, user_id, url)
            raise HTTPException(
                status_code=500, detail=f"Failed to update BigQuery: {e}")

        # Fetch the complete row data
        row = fetch_bq_row(dataset_table, url)

        if row:
            return {
                "status": "success",
                "url": url,
                "thread_file": thread_file,
                "assigned_to": user_id,
                "assigned_at": datetime.utcnow().isoformat(),
                "bigquery_row": row
            }
        else:
            logger.warning(
                f"Could not fetch row data for {url} after assignment")
            return {
                "status": "failed",
                "url": url,
                "thread_file": thread_file,
                "assigned_to": user_id,
                "bigquery_row": None,
                "error": "Could not fetch row data after assignment"
            }

    logger.info("No claimable tasks found in any thread file")
    return None

# ✅ GCS blob claiming logic


def try_claim_in_blob(blob_name: str, user_id: str):
    """
    Try to claim a pending task from a specific GCS blob
    """
    bucket = gcs_client.bucket(BUCKET_NAME)
    blob = bucket.blob(blob_name)

    if not blob.exists():
        logger.warning(f"Blob does not exist: {blob_name}")
        return None

    # Get current generation for atomic updates
    generation = blob.generation

    try:
        text = blob.download_as_text() or "[]"
        data = json.loads(text)
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in blob {blob_name}: {e}")
        return None
    except Exception as e:
        logger.error(f"Error reading blob {blob_name}: {e}")
        return None

    # Look for pending tasks
    for idx, item in enumerate(data):
        status = item.get("status") or item.get("walmart", {}).get("status")

        if status == "pending":
            url = item.get("url") or item.get("walmart", {}).get("url")

            if not url:
                logger.warning(
                    f"Found pending item without URL in {blob_name}")
                continue

            # Claim the task
            item["status"] = "assigned"
            item["assigned_to"] = user_id
            item["assigned_at"] = datetime.utcnow().isoformat()

            # Ensure URL is at top level
            if "url" not in item and url:
                item["url"] = url

            # Upload updated data atomically
            new_text = json.dumps(data, indent=2, ensure_ascii=False)

            try:
                blob.upload_from_string(
                    new_text,
                    content_type="application/json",
                    if_generation_match=generation
                )
                logger.info(f"Successfully claimed task {url} in {blob_name}")
                return {"url": url, "thread_file": blob_name}

            except PreconditionFailed:
                logger.warning(
                    f"Concurrent modification of {blob_name}, skipping")
                return None
            except Exception as e:
                logger.error(f"Failed to update blob {blob_name}: {e}")
                return None

    return None

# ✅ BigQuery functions


def fetch_bq_row(dataset_table: str, url: str) -> dict | None:
    """
    Fetch complete row data from BigQuery for a given URL
    """
    query = f"""
    SELECT * 
    FROM `ops-excellence.{dataset_table}` 
    WHERE website_link = @url 
    LIMIT 1
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[bigquery.ScalarQueryParameter("url", "STRING", url)]
    )

    try:
        query_job = bq_client.query(query, job_config=job_config)
        results = query_job.result()

        for row in results:
            # Convert BigQuery Row to dictionary
            row_dict = dict(row)
            logger.debug(
                f"Fetched row data for {url}: {list(row_dict.keys())}")
            return row_dict

        logger.warning(f"No row found in BigQuery for URL: {url}")
        return None

    except Exception as e:
        logger.error(f"Error fetching BigQuery row for {url}: {e}")
        raise


def bq_url_exists(dataset_table: str, url: str) -> bool:
    """
    Check if URL exists in BigQuery table
    """
    query = f"""
    SELECT COUNT(*) as count 
    FROM `ops-excellence.{dataset_table}` 
    WHERE website_link = @url
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[bigquery.ScalarQueryParameter("url", "STRING", url)]
    )

    try:
        query_job = bq_client.query(query, job_config=job_config)
        results = query_job.result()

        for row in results:
            exists = row.count > 0
            logger.debug(f"URL {url} exists in BigQuery: {exists}")
            return exists

        return False

    except Exception as e:
        logger.error(f"Error checking URL existence in BigQuery: {e}")
        raise


def mark_url_assigned_in_bq(dataset_table: str, url: str, user_id: str):
    """
    Update BigQuery to mark URL as assigned to user
    """
    query = f"""
    UPDATE `ops-excellence.{dataset_table}` 
    SET 
        assign = @user_id,
        status = 'Assigned', 
        item_status = 'check',
        assigned_at = CURRENT_TIMESTAMP()
    WHERE website_link = @url
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("user_id", "STRING", user_id),
            bigquery.ScalarQueryParameter("url", "STRING", url)
        ]
    )

    try:
        query_job = bq_client.query(query, job_config=job_config)
        query_job.result()  # Wait for completion

        # Verify update was successful
        if query_job.num_dml_affected_rows == 0:
            raise Exception(f"No rows updated for URL: {url}")

        logger.info(f"Successfully updated BigQuery assignment for {url}")
        return True

    except Exception as e:
        logger.error(f"Error updating BigQuery assignment for {url}: {e}")
        raise

# ✅ Utility functions


def list_thread_files():
    """
    Return list of thread blob names under THREAD_FOLDER
    """
    try:
        bucket = gcs_client.bucket(BUCKET_NAME)
        blobs = gcs_client.list_blobs(bucket, prefix=f"{THREAD_FOLDER}/")
        files = [b.name for b in blobs if b.name.endswith(".json")]

        logger.info(f"Found {len(files)} thread files in GCS")
        return files

    except Exception as e:
        logger.error(f"Error listing thread files: {e}")
        return []


def revert_claim_in_blob(blob_name: str, user_id: str, url: str):
    """
    Revert a claim in GCS blob if BigQuery operations fail
    """
    bucket = gcs_client.bucket(BUCKET_NAME)
    blob = bucket.blob(blob_name)

    try:
        if not blob.exists():
            logger.warning(
                f"Cannot revert claim - blob doesn't exist: {blob_name}")
            return False

        generation = blob.generation
        text = blob.download_as_text() or "[]"
        data = json.loads(text)

        changed = False
        for item in data:
            item_url = item.get("url") or item.get("walmart", {}).get("url")

            if (item_url == url and
                item.get("assigned_to") == user_id and
                    item.get("status") == "assigned"):

                # Revert the claim
                item["status"] = "pending"
                item["assigned_to"] = None
                item.pop("assigned_at", None)
                changed = True
                logger.info(f"Reverted claim for {url} in {blob_name}")
                break

        if changed:
            blob.upload_from_string(
                json.dumps(data, indent=2, ensure_ascii=False),
                content_type="application/json",
                if_generation_match=generation
            )
            return True

    except PreconditionFailed:
        logger.warning(f"Concurrent modification during revert of {blob_name}")
        return False
    except Exception as e:
        logger.error(f"Error reverting claim in {blob_name}: {e}")
        return False

    return False

# ✅ Health check endpoint


@router.get("/health")
async def health_check():
    """
    Health check endpoint for the claim service
    """
    try:
        # Test GCS connectivity
        bucket = gcs_client.bucket(BUCKET_NAME)
        bucket.exists()

        # Test BigQuery connectivity
        query = "SELECT 1 as test"
        query_job = bq_client.query(query)
        query_job.result()

        return {
            "status": "healthy",
            "services": {
                "gcs": "connected",
                "bigquery": "connected"
            },
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

# ✅ Debug endpoint to check thread files


@router.get("/debug/thread-files")
async def debug_thread_files():
    """
    Debug endpoint to check available thread files
    """
    try:
        files = list_thread_files()
        file_details = []

        for file_name in files[:5]:  # Limit to first 5 files
            bucket = gcs_client.bucket(BUCKET_NAME)
            blob = bucket.blob(file_name)

            if blob.exists():
                try:
                    text = blob.download_as_text()
                    data = json.loads(text)

                    pending_count = sum(1 for item in data
                                        if (item.get("status") or
                                            item.get("walmart", {}).get("status")) == "pending")

                    file_details.append({
                        "file": file_name,
                        "total_items": len(data),
                        "pending_tasks": pending_count,
                        "last_modified": blob.updated.isoformat() if blob.updated else None
                    })

                except Exception as e:
                    file_details.append({
                        "file": file_name,
                        "error": str(e)
                    })

        return {
            "total_files": len(files),
            "sample_files": file_details,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Debug error: {str(e)}")


app = FastAPI(title="Product Description API")

# ---------------- Helper Function ----------------


class SiteSummary(BaseModel):
    attribute: str
    selectedSources: List[str] = []
    selectedValues: List[str] = []
    walmartValidation: Optional[str] = ""
    llmValidation: Optional[str] = ""
    walmartComment: Optional[str] = ""
    llmComment: Optional[str] = ""


class ProductRequest(BaseModel):
    product_name: str
    product_attributes: Optional[Dict[str, str]] = {}
    site_summaries: Optional[List[SiteSummary]] = []
    finalValues: Optional[List[Dict[str, Any]]] = []


class ProductResponse(BaseModel):
    short_summary: str
    long_summary: str
    abot_this_item: Optional[str] = None  # kept as per your definition

# ---------------- Helper ----------------


def generate_from_attributes(
    product_name: str,
    product_attributes: dict,
    site_summaries: list
) -> tuple[str, str, str]:
    """
    Generate short summary, long summary, and about_this_item
    from product attributes and site summaries.
    """
    # ---------------- Prepare input texts ----------------
    # Product attributes
    attributes_text = "\n".join(
        [f"- {k}: {v}" for k, v in product_attributes.items()]) or "N/A"

    # Site summaries
    site_summaries_text = ""
    for s in site_summaries:
        values = ", ".join(s.selectedValues) if s.selectedValues else "N/A"
        sources = ", ".join(s.selectedSources) if s.selectedSources else "N/A"
        site_summaries_text += (
            f"Attribute: {s.attribute}\n"
            f"Values: {values}\n"
            f"Sources: {sources}\n"
            f"Walmart Validation: {s.walmartValidation or 'N/A'}\n"
            f"LLM Validation: {s.llmValidation or 'N/A'}\n"
            f"Walmart Comment: {s.walmartComment or 'N/A'}\n"
            f"LLM Comment: {s.llmComment or 'N/A'}\n\n"
        )

    # ---------------- Build prompt ----------------
    context = (
        "You are given several site summaries, each with a short and a long version. "
        "Merge the important ideas into one unified description, naturally including key product attributes. "
        "Do not compare sites or add extra information. "
        "For each attribute "
        "explain its meaning and relevance to the customer in clear, natural language. "
        "Present the information in a well-structured short summary and a longer summary. "
        "The short summary should be concise (at least 55 words) focusing on the product highlights. "
        "The long summary must contain bullet points., elaborating on performance, durability, compatibility, and ease of use."
    )
    if product_attributes:
        context += "Product Attributes:\n"
        for key, value in product_attributes.items():
            context += f"- {key}: {value}\n"

    if site_summaries:
        context += "\nSite Summaries:\n"
        for s in site_summaries:
            context += f"- {s}\n"

    # Output instructions with rules
    output_instructions = []
    if True:  # Always generate short summary
        output_instructions.append(
            "BEST SHORT SUMMARY:\n"
            "<your short summary here, attributes naturally integrated>"
            # "using the product attributes. "
            "The short summary should be concise (at least 55 words) highlighting key features and benefits. "
            "Explicitly explain what each attribute means for the customer (e.g., why the material matters, why dimensions are useful, "
            "why compatibility and vehicle fitment are important). "
            "Avoid technical jargon without explanation and make it engaging for buyers."
            """RULES:  
1. The Short Description should be in paragraph form.  
2. Must be consistent with the actual product.  
3. Remove promotional text like warranty, free shipping, or discounts.  
4. Remove text that promotes another retailer.  
5. Keep ™, ®, ©. Remove unwanted symbols.  
6. Proper casing required.  
7. The Short Description should not repeat the Long Description.  
8. Remove duplicate text across Short and Long Descriptions.  
9. Short Description is for describing the product and brand/manufacturer story.  
10. If Short Description entirely in bullet form → move to Long Description and fail Short.  
11. If Short partially contains bullet-form features/specs → move those to Long Description.  
12. Short Description must clearly describe the product. If not, fail.  
13. Fix grammar and spelling errors.  
14. Leave HTML if present.  
15. Spell check is required.  
16. Verify dietary/nutritional claims.  
17. Minimum Word Count rule: if word count < minimum → fail.  

TASK INSTRUCTION:  
Given a product’s Short Description, Product Name, and Long Description:  

Clean and transform the Short Description following all rules above.  
If the Short Description fails the rules, output: "FAIL SHORT DESCRIPTION".  
If you move text to the Long Description, clearly mark it under "UPDATED LONG DESCRIPTION".  
Output must follow this format:  
"""

        )

    if True:  # Always generate long summary
        output_instructions.append("""
TASK:  
Given a product’s Short Description, Product Name, and Long Description → clean and transform the Long Description following the rules below.  

OUTPUT FORMAT:  
- If valid, output must begin with:  
  BEST LONG SUMMARY:  
  * **Property Name**: description  
  * **Property Name**: description  

- If invalid → output exactly:  
  FAIL LONG DESCRIPTION  

- If text is moved to the Short Description, output it clearly under:  
  UPDATED SHORT DESCRIPTION: <new text>  

---

CONTENT RULES:
-Srictly follow this

1. The summary must contain bullet points. Then bullet character must be "*". 
2. Each bullet must begin with: "Property Name: description".  
3. Each bullet Property Name must be bold.
4. Mandatory bullets (must always appear in the summary):
   - **Product Name**: Exact match of the Product Name attribute.do not include the word "Product Name:".  - **Product Type**: Describe what the product is.
   - **Application**: Specify the use or intended purpose of the product.
   - **Utility**: Explain how the product is useful (factual, non-marketing).
   - **Key Features**: Highlight the standout features of the product.
   - Include exactly one bullet per mandatory attribute.
   - If an attribute value is missing, skip it. Do not invent placeholders.
   - Include these bullets at the start of the description, in the order listed above.
5. All passed attributes with valid values** (e.g., material, dimensions, weight, brand, compatibility, condition or any other PRODUCT ATTRIBUTES: ) must be included as separate bullets**. Do not skip any valid attribute.
6. Descriptions must remain factual only.
   - No interpretation, persuasion, or explanations.
   - No vague/repetitive wording (e.g., “ensures you receive,” “clearly indicates”).
7. No second-person language (“you,” “your”).
8. No marketing fluff. Tone must be technical and product-focused.
9. Grammar, spelling, and casing must always be correct.
10. If an attribute value is missing, skip it. Do not invent placeholders.
11. If the format is not bullet points → return FAIL LONG DESCRIPTION.
12. Content must be consistent with the actual product.
13. Remove promotional text about discounts, shipping, warranty, or guaranty.

---

STRUCTURAL RULES:  
1. If the Product Name is present in the Long Description in bold above the bullet points, leave it. If the Product Name differs, update it to match the Product Name attribute.  
2. Long Description must be in bullet point form. Paragraph-only descriptions → reject.  
3. Remove promotional text about discounts, shipping, warranties, or guarantees. Keep factual mentions of accessories, compatibility, or product variations.  
4. Remove external retailer mentions/redirects. Keep factual mentions like “Works with Amazon Alexa.”  
5. Symbols: keep ™, ®, ©. Remove €, †, ‡, •, ¢, £, ¥, ±, ¶, ~, â, ???.  
6. Ensure proper casing. ALL CAPS/lowercase is only allowed if validated Brand names or trademarks.  
7. Do not repeat the Short Description. Delete duplicate sentences across Short/Long descriptions.  
8. Brand/Manufacturer Story Handling:  
   - If in paragraph form (>2 sentences), move it to the end of the Short Description.  
   - If it is about features/specifications, keep in Long Description.  
9. If Long Description is a full paragraph and Short Description is empty → move it to Short Description and fail Long Description.  
10. If only part of the Long Description is a brand/manufacturer paragraph → move that part to Short Description.  
11. Must clearly describe the product. If unclear → fail Long Description.  
12. Correct grammatical/spelling errors.  
13. If HTML is present, leave it unchanged. Do not add/remove tags.  
14. If non-HTML bullet points are present, leave them; Ops team will reformat.  
15. Verify any nutritional/dietary claim against product images. Remove if not visible on packaging.  

---

EXAMPLES:  

. Bilstein B4 OE Replacement Strut Assembly(only product name should be there just name dont used product name)
• Material: Constructed from carbon steel for strength and durability.  
• Handle Design: Features a cushioned blue grip for comfort and skid resistance.  
• Ratchet Technology: Provides 360° swing for ease of positioning.  
• Application: Designed for disc brake systems with twin and quad piston calipers.  
• Usage: Suitable for both fixed and floating caliper systems.  
• Function: Expands and retracts spreading plates with simple handle movement.  
• Adjustment: Direction controlled by a reversible ratchet switch.  
• Compatibility: Works with single and twin-piston floating calipers.  
• Package Contents: Includes one brake piston compression tool.  
• Intended Use: Practical for professional mechanics and home garages.  

- ❌ Incorrect Output:  
"FAIL LONG DESCRIPTION" (when rules are not followed).  

"""

                                   )

    output_instructions_text = "\n".join(output_instructions)

    prompt_template = """
{context}

{output_instructions}

PRODUCT ATTRIBUTES:
{attributes_text}

SITE SUMMARIES:
{site_summaries_text}
"""
    prompt = prompt_template.format(
        context=context,
        output_instructions=output_instructions_text,
        attributes_text=attributes_text,
        site_summaries_text=site_summaries_text or "N/A"
    )

    # ---------------- Call Gemini API ----------------
    # try:
    #     model = genai.GenerativeModel("gemini-2.5-flash-lite")
    #     response = model.generate_content(prompt)
    #     result_text = response.text
    #     print("result_text:", result_text)
    #     print("prompt:", prompt)
    # except Exception as e:
    #     print("Gemini API error:", e)
    #     result_text = ""
    try:
        model = genai.GenerativeModel(
            'gemini-2.5-flash',
            # system_instruction='you are a expert product description writer',
            generation_config=genai.GenerationConfig(
                # max_output_tokens=2048,
                top_k=2,
                top_p=0.5,
                temperature=0.2,
                # response_mime_type='text/plain',
                # stop_sequences=['\n'],
            )
        )
        response = model.generate_content(prompt)
        result_text = response.text
        print("result_text:", result_text)
        # print("prompt:", prompt)
    except Exception as e:
        print("Gemini API error:", e)
        result_text = ""
    # ---------------- Parse output ----------------
    # Defaults
    short_summary = "No short summary generated."
    long_summary = "No long summary generated."
    about_this_item = None

    try:
        short_summary = result_text.split("BEST SHORT SUMMARY:")[1].split("BEST LONG SUMMARY:")[0].strip()
    except Exception:
        try:
            short_summary = result_text.split("\n\n")[0].strip()
        except Exception:
            pass

    try:
        long_summary = result_text.split("BEST LONG SUMMARY:")[1].split("BEST ABOUT THIS ITEM:")[0].strip()
    # Remove leading "â€¢" if present
        long_summary = "\n".join([line.lstrip("â€¢ ").strip() for line in long_summary.splitlines() if line.strip()])
    except Exception:
        long_summary = "No long summary generated."


    try:
        about_this_item = result_text.split("BEST ABOUT THIS ITEM:")[1].strip()
    except Exception:
        # fallback: extract 2 bullets from long_summary if possible
        bullets = [line for line in long_summary.splitlines() if line.strip().startswith("â€¢")]
        about_this_item = "\n".join(bullets[:2]) if bullets else None

    # ---------------- Always return all three ----------------
    # return short_summary, long_summary, about_this_item
        # ---------------- Apply Second LLM Refinement ----------------
    print("Applying second LLM refinement...")
    refined_short, refined_long, refined_about = apply_second_llm_refinement(
        product_name=product_name,
        initial_short=short_summary,
        initial_long=long_summary,
        initial_about=about_this_item,
        product_attributes=product_attributes
    )

    return refined_short, refined_long, refined_about


        # ---------------- Always return all three ----------------
# ---------------- Second LLM Refinement Function ----------------
def apply_second_llm_refinement(
    product_name: str,
    initial_short: str,
    initial_long: str,
    initial_about: str,
    product_attributes: dict
) -> tuple[str, str, str]:
    """
    Apply second LLM pass to refine and improve the initial response.
    """
    refinement_prompt = f"""
PRODUCT: {product_name}

INITIAL GENERATED CONTENT:

SHORT SUMMARY:
{initial_short}

LONG SUMMARY:
{initial_long}

ABOUT THIS ITEM:
{initial_about or 'Not provided'}

PRODUCT ATTRIBUTES:
{product_attributes}


IMPORTANT FORMATTING RULES FOR LONG SUMMARY:
- For "Product Name" bullet: Show ONLY the product name value, NOT the label
- Example: Instead of "**Product Name**: Bilstein 5160 Series" 
- Show: "Bilstein 5160 Series 5165 Series 8.9in. Travel 46mm Monotube Shock Absorber"
- Remove the "**Product Name**: " prefix entirely
- Remove promotional text about discounts, shipping, warranty, or guaranty.

OUTPUT FORMAT:
REFINED SHORT SUMMARY: [your refined short summary version]
REFINED LONG SUMMARY: [your refined long summary version]
"""



    try:
        model = genai.GenerativeModel('gemini-2.5-flash')
        response = model.generate_content(refinement_prompt)
        refined_text = response.text
        print("Second LLM refinement response:", refined_text)
    except Exception as e:
        print(f"Second LLM refinement error: {e}")
        return initial_short, initial_long, initial_about

    # Parse the refined response
    refined_short = initial_short
    refined_long = initial_long
    refined_about = initial_about

    try:
        if "REFINED SHORT SUMMARY:" in refined_text:
            parts = refined_text.split("REFINED SHORT SUMMARY:")[1]
            if "REFINED LONG SUMMARY:" in parts:
                refined_short = parts.split("REFINED LONG SUMMARY:")[0].strip()
                remaining = parts.split("REFINED LONG SUMMARY:")[1]
                if "REFINED ABOUT THIS ITEM:" in remaining:
                    refined_long = remaining.split("REFINED ABOUT THIS ITEM:")[0].strip()
                    refined_about = remaining.split("REFINED ABOUT THIS ITEM:")[1].strip()
                else:
                    refined_long = remaining.strip()
            else:
                refined_short = parts.strip()
    except Exception as e:
        print(f"Error parsing second LLM response: {e}")

    return refined_short, refined_long, refined_about



# ---------------- Endpoint ----------------
@router.post("/process_product", response_model=ProductResponse)
async def process_product(data: ProductRequest):
    logger.info(f"Processing product: {data.product_name}")

    # Generate summaries
    short_summary, long_summary, about_this_item = generate_from_attributes(
        product_name=data.product_name,
        product_attributes=data.product_attributes or {},
        site_summaries=data.site_summaries or []
    )

    # Build response
    return ProductResponse(
        short_summary=short_summary,
        long_summary=long_summary,
        abot_this_item=about_this_item
    )