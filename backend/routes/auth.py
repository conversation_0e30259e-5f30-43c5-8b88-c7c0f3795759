import time
import logging
from fastapi import APIRouter, HTTPException
from google.cloud import bigquery

from models.user import UserSignup, UserLogin
from utils.security import hash_password, verify_password
from database import bq_client
from config import PROJECT_ID, DATASET_ID, USERS_TABLE

router = APIRouter()

@router.post("/signup")
def signup(user: UserSignup):
    # 1. Check if username already exists
    query = f"""
    SELECT username FROM `{PROJECT_ID}.{DATASET_ID}.{USERS_TABLE}`
    WHERE username = @username
    """
    job_config = bigquery.QueryJobConfig(
        query_parameters=[bigquery.ScalarQueryParameter("username", "STRING", user.username)]
    )
    results = list(bq_client.query(query, job_config=job_config).result())

    if results:
        raise HTTPException(status_code=400, detail="Username already exists")

    # 2. Prepare row to insert
    rows_to_insert = [{
        "id": int(time.time() * 1000),   # ✅ unique integer id based on timestamp
        "username": user.username,
        "email": user.email,
        "password": hash_password(user.password),
        "role": user.role,
        "created_at": None,
        "updated_at": None
    }]

    # 3. Insert into BigQuery
    table_ref = f"{PROJECT_ID}.{DATASET_ID}.{USERS_TABLE}"
    errors = bq_client.insert_rows_json(table_ref, rows_to_insert)

    if errors:
        logging.error(f"BigQuery insert error: {errors}")
        raise HTTPException(status_code=500, detail=f"Failed to insert: {errors}")

    return {"message": "User registered successfully ✅"}


@router.post("/login")
def login(user: UserLogin):
    query = f"""
    SELECT username, email, password, role 
    FROM `{PROJECT_ID}.{DATASET_ID}.{USERS_TABLE}`
    WHERE username = @username
    """
    job_config = bigquery.QueryJobConfig(
        query_parameters=[bigquery.ScalarQueryParameter("username", "STRING", user.username)]
    )
    rows = list(bq_client.query(query, job_config=job_config).result())

    if not rows:
        raise HTTPException(status_code=400, detail="Invalid username or password")

    stored_password = rows[0]["password"]

    if not verify_password(user.password, stored_password):
        raise HTTPException(status_code=400, detail="Invalid username or password")

    return {
        "message": "Login successful 🎉",
        "username": rows[0]["username"],
        "email": rows[0]["email"],
        "role": rows[0]["role"]
    }
