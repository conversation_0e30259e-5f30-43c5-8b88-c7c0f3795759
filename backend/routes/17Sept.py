# integrated_crawlbase_serper_dynamic.py
import os
import logging
import json
import re
import http.client
import requests
import google.generativeai as genai
import pandas as pd
from google.cloud import storage
from urllib.parse import urlparse
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from requests.exceptions import HTTPError, RequestException
import time
from google.oauth2 import service_account
from fastapi import FastAPI, HTTPException, Form, APIRouter
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from pydantic import BaseModel
from typing import Optional
from fastapi.responses import JSONResponse
from config import GEMINI_API_KEY  
gemini_client = genai.GenerativeModel("gemini-1.5-flash")
# CRAWLBASE_API_KEY = os.getenv("CRAWLBASE_API_KEY", "czDsWv_NW8_P0Lrh3ZMeTQ")
SERPER_API_KEY = os.getenv("SERPER_API_KEY", "7880b8ab99f5d1aaa9acb5a6e1829bce0ee30bf8")
# SERPER_API_KEY = os.getenv("SERPER_API_KEY", "379be5ecc436019c38a0330479b8144f8f53f8e3")

app = FastAPI(title="Product Extraction API")
router = APIRouter()
logger = logging.getLogger("FastAPI_Logger")


required_attributes = [
    "Amps", "Assembled Product Height", "Assembled Product Length",  "Assembled Product Weight","Assembled Product Width","Automotive Horn Type",
    "Automotive Replacement Belt Type","Automotive Specialty Part Type","Automotive Specialty Tool Type","Automotive Valve Stem & Cap Type",
    "Bore x Stroke",  "Brake System Replacement Part & Hardware Type","Brand","Case Material","Color","Color Category", "Compatible Cars",
    "Compression Ratio","Condition","Cooling System","Correct Product Name","Correct Product Type","Count Per Pack","Decibel Rating",
    "Dimensions","Engine Cylinder Configuration","Engine Cylinder Head Type","Engine Displacement","Fastener Drive Size","Fastener Thread Length",
    "Features","Finish","Fuel Capacity","Fuel Injector Type","Fuel Type","Gear Ratio","Hardware Nut Type","Has Written Warranty","Horsepower",
    "Inlet Diameter","Items Included","Length Range","Lug Nut Seat Style","Lug Nut Type","Main Image URL","Manufacturer","Manufacturer Part Number",
    "Material","Maximum Length","Maximum Oil Capacity","Maximum RPM","Minimum Length","Model","Motor Starter Type",
    "Muffler Height","Muffler Length","Muffler Type","Muffler Width","Multipack Quantity","Number of Engine Cycles","Odometer Type",
    "Orientation","Outlet Diameter","Piece Count","Power Take-Off","Power Type",
    "Product Long Description","Product Name","Product Net Content Parent|Product Net Content","Product Net Content Parent|Product Net Content UOM",
    "Product Secondary Image URL",
    "Product Short Description""Product Type","Pulley Type",
    "Recommended Use","Resonator Type","Rotation Direction","Series","Shaft Length","Size","Speedometer Type",
    "Stroke Length","Tachometer Type", "Third Party Accreditation Symbol on Product Package Code","Thread Size","Torque",
    "Total Count","Tube Diameter","Vehicle Fitment Type","Vehicle Mount Location","Vehicle Type",
    "Volts","Warranty Information","Warranty URL","Website Link","Wheel Bearing Type","Wheel Drive Options","Wheel Lug Pattern"
]
class ExtractionRequest(BaseModel):
    google_sheet_url: str
    bucket_name: str
    blob_name: Optional[str] = "api_test_data/products.json"


def get_domain_name(url: str, brand: str = "") -> str:
    """
    Extracts a clean, normalized domain name from a URL.
    Dynamically normalizes brand domains.
    """
    try:
        parsed = urlparse(url)
        hostname = (parsed.hostname or "").lower()
        if hostname.startswith("www."):
            hostname = hostname[4:]

        base = hostname.split(".")[0]  # first part only

        # Normalize brand domains dynamically
        if brand:
            brand_clean = re.sub(r"[^a-z0-9]", "", brand.lower())
            if brand_clean in base:
                return brand_clean

        return base or "unknown"
    except Exception:
        return "unknown"
# CONFIG



def save_to_gcs_json(data: list[dict], bucket_name: str, blob_name: str, service_account_file="ops-excellence.json"):
    """
    Save list of dicts to GCS as a single JSON file using a service account.
    """
    try:
        # Load service account credentials
        credentials = service_account.Credentials.from_service_account_file(service_account_file)

        # Initialize storage client with credentials
        client = storage.Client(credentials=credentials, project=credentials.project_id)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_name)

        # Convert data to JSON string
        json_data = json.dumps(data, indent=2, ensure_ascii=False)

        # Upload
        blob.upload_from_string(json_data, content_type="application/json")
        print(f"✅ Written JSON with {len(data)} product entries to gs://{bucket_name}/{blob_name}")

    except Exception as e:
        print(f"❌ Failed to save JSON to GCS: {e}")

BASE_DIR = os.path.dirname(os.path.abspath(__file__))  # Gets current file's directory
SCHEMA_FILE = os.path.join(BASE_DIR, "output.json")
DOMAIN = "Vehicle"

genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel("gemini-1.5-flash")

def get_gsheet_client():
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds = ServiceAccountCredentials.from_json_keyfile_name("ops-excellence.json", scope)
    return gspread.authorize(creds)

#google sheet
def flatten_df(df: pd.DataFrame) -> pd.DataFrame:
    """Convert lists/dicts/None into clean strings for Google Sheets."""
    def flatten_value(val):
        if isinstance(val, list):
            return "; ".join(map(str, val))   # lists → "a; b; c"
        elif isinstance(val, dict):
            return json.dumps(val, ensure_ascii=False)  # dicts → JSON string
        return "" if val is None else str(val)
    return df.applymap(flatten_value)


def save_to_gsheet(spreadsheet_url, sheet_name, df):
    client = get_gsheet_client()
    spreadsheet = client.open_by_url(spreadsheet_url)

    # If sheet already exists, delete it
    try:
        worksheet = spreadsheet.worksheet(sheet_name)
        spreadsheet.del_worksheet(worksheet)
    except Exception:
        pass

    # Flatten values before uploading
    df = flatten_df(df)

    # Create new worksheet
    worksheet = spreadsheet.add_worksheet(
        title=sheet_name,
        rows=str(len(df)+10),
        cols=str(len(df.columns)+10)
    )

    worksheet.update([df.columns.values.tolist()] + df.values.tolist())
    print(f"✅ Written {len(df)} rows to Google Sheet tab: {sheet_name}")


# schema 
def load_schema(path: str) -> dict:
    with open(path, "r", encoding="utf-8") as f:
        return json.load(f)


def pick_product_type(schema: dict, domain: str, candidates: list[str]) -> str | None:
    if domain not in schema:
        return None
    for cand in candidates:
        if cand in schema[domain]:
            return cand
    for k, v in schema[domain].items():
        if isinstance(v, dict) and "attributes" in v:
            return k
    return None


def safe_extend_list(lst, items):
    """Append items to lst safely even if items is int, None, or single dict."""
    if isinstance(items, list):
        return lst + items
    elif items is not None:
        return lst + [items]
    return lst

def build_attr_instructions(schema: dict, domain: str, product_type: str):
    meta = schema.get(domain, {}).get(product_type, {}).get("attributes", [])
    attr_map, lines = {}, []
    for attr in meta:
        raw_name = attr.get("attribute_name", "")
        req_level = attr.get("requirement_level", "").strip()
        display_name = attr.get("display_name", raw_name)

        # ✅ Concatenate only once for output
        concat_name = f"{raw_name} ({req_level})" if req_level else raw_name

        # Map raw_name → concat_name
        attr_map[raw_name] = concat_name

        line = (
            f"- Attribute: {concat_name} | "
            f"Required: {req_level} | "
            f"Definition: {attr.get('definition','')} | "
            f"Examples: {attr.get('examples','')} | "
            f"Closed: {attr.get('is_closed_list','')} | "
            f"Multi: {attr.get('is_multi_select','')} | "
            f"Units: {', '.join(attr.get('acceptable_attributes', {}).get('units', [])) if attr.get('acceptable_attributes') else 'Any'} | "
            f"Values: {', '.join(attr.get('acceptable_attributes', {}).get('values', [])) if attr.get('acceptable_attributes') else 'Any'}"
        )
        # print("^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^",flush=True)
        # print("line",line)
        lines.append(line)

    return meta, attr_map, "\n".join(lines)

def fetch_plain_text_via_crawlbase(url, retries=5, backoff_factor=2):
    """Fetch plain text via Crawlbase with retry & backoff."""
    api_token = os.getenv("CRAWLBASE_TOKEN", "czDsWv_NW8_P0Lrh3ZMeTQ")
    crawl_url = f"https://api.crawlbase.com/?token={api_token}&url={url}"

    for attempt in range(1, retries + 1):
        try:
            r = requests.get(crawl_url, timeout=60)
            r.raise_for_status()
            return r.text

        except HTTPError as e:
            print(f"⚠️ HTTP error {e.response.status_code} on attempt {attempt}/{retries} for {url}")
        except RequestException as e:
            print(f"⚠️ Request error {e} on attempt {attempt}/{retries} for {url}")

        # Exponential backoff
        sleep_time = backoff_factor ** attempt
        print(f"⏳ Retrying in {sleep_time}s...")
        time.sleep(sleep_time)

    print(f"❌ Failed to fetch {url} after {retries} retries.")
    return None

def safe_json_loads(text: str) -> dict:
    try:
        return json.loads(text)
    except Exception:
        m = re.search(r"\{.*\}", text, flags=re.DOTALL)
        if m:
            return json.loads(m.group(0).strip())
        return {"attributes": {}, "extra": {}}


def guess_brand_domains(brand: str) -> list[str]:
    brand = brand.lower().strip()
    base = re.sub(r"[^a-z0-9]", "", brand)
    return [
        f"{base}.com", f"{base}.co.in", f"{base}.in",
        f"{base}india.com", f"{base}official.com",
        f"{base}global.com", f"{base}store.com",
        f"{base}shop.com", f"{base}products.com",
        f"{base}industries.com"
    ]


def extract_all_schema_attributes(
    url: str,
    schema: dict,
    domain: str,
    product_type: str,
    required_keys: list,
    retries=5
):
    plain = fetch_plain_text_via_crawlbase(url, retries=retries)
   
    if not plain:
        print(f"⚠️ Skipping URL (failed fetch): {url}")
        return {"url": url, "attributes": {}, "extra": {}}

    # Build schema context
    meta, attr_map, attr_instructions = build_attr_instructions(schema, domain, product_type)

    
    schema_fields = [a.get("attribute_name") for a in meta if a.get("attribute_name")]
    prompt = f"""
    You are an AI product attribute extractor.

    INPUTS:
    PRODUCT PAGE TEXT: {plain}
    ATTRIBUTE CONTEXT (from schema): {attr_instructions}

    TASK:
    For each attribute_name listed in ATTRIBUTE CONTEXT:
    1. Search for its value inside PRODUCT PAGE TEXT.
    2. Follow schema rules.
    3. If an attribute is missing, return "".
    4. Ensure every attribute_name from the schema is present in the output.
    5.Some important rules to remember while fetching from output.json.
      Rules:
        Use definition, examples, and validation_instructions to guide you.
        - Respect requirement rules:
            • required → must extract value if available
            • recommended → good to extract but not mandatory
            • conditionally required → only extract if condition matches
        - Respect closed list rules:
            • If is_closed_list = true → value MUST be from acceptable values list
            • If not matched → leave empty
        - Respect multi-select rules:
            • If is_multi_select = true → allow multiple comma separated values
            • Otherwise only one value allowed
        - Output JSON where keys = attribute_name.
        -For variant name go through:
            -Requirement rule 
            -is closed value if its true :
                    -then in place of Variant Attribute Names provide(necessary) me the value for the attributed in acceptable values that are present also try to provide its value(e.g.attribute_name:attribut from acceptable value(value)).
                    -also look for the attributes oin extracted attributes there may be attribute.
            -else show "NA"

    OUTPUT FORMAT:
    {{
    "attributes": {{ ... }},
    "extra": {{
        "brand": "", "mpn": "", "product_type": "",
        "vehicle_make": "", "vehicle_model": "", "vehicle_year": "", "query": ""
    }}
    }}

    PAGE TEXT (truncated):
    {plain[:1600]}
    """
    try:
        resp = gemini_model.generate_content(prompt)
        raw = resp.text or ""
        data = safe_json_loads(raw.strip())
    except Exception as e:
        print(f"⚠️ Gemini extraction failed for {url}: {e}")
        data = {"attributes": {}, "extra": {}}
    
    extracted_attrs = data.get("attributes") or {}
    cleaned_attrs = {key: extracted_attrs.get(key, "") for key in required_keys}

    # for raw_name in schema_fields:
    #     concat_name = attr_map.get(raw_name, raw_name)
    #     value = extracted_attrs.get(concat_name) or extracted_attrs.get(raw_name, "")
    #     cleaned_attrs[concat_name] = value

    
    cleaned_attrs = dict(sorted(cleaned_attrs.items(), key=lambda x: x[0].lower()))

    extras = data.get("extra") or {}
    for k in ["brand", "mpn", "product_type", "vehicle_make", "vehicle_model", "vehicle_year", "query"]:
        extras.setdefault(k, "")

    return {"url": url, "attributes": cleaned_attrs, "extra": extra}

def filter_product_pages(results: list) -> list:
    """Remove category/collection/store landing pages from search results."""
    bad_keywords = ["category", "categories", "collections", "series", 
                    "accessories", "shop", "store", "mounts", "parts"]

    filtered = [r for r in results if not any(b in r["link"].lower() for b in bad_keywords)]
    return filtered or results  # fallback if everything gets filtered out

def build_unique_query(walmart_extracted: dict) -> str:
    """Build query string optimized for product pages (not category pages)."""

    extra = walmart_extracted.get("extra") or {}  # ✅ safeguard if None
    brand = (extra.get("brand") or "").strip()
    mpn = (extra.get("mpn") or "").strip()
    manufacturer_no = walmart_extracted.get("attributes", {}).get("manufacturer_number", "")
    model_no = walmart_extracted.get("attributes", {}).get("model_number", "")
    title = walmart_extracted.get("attributes", {}).get("product_name", "")

    # Start with product name (forces specificity)
    parts = []
    if title:
        parts.append(f"\"{title}\"")
    if brand:
        parts.append(f"\"{brand}\"")

    # Strong identifiers
    if mpn:
        parts.append(f"\"{mpn}\"")
    elif manufacturer_no:
        parts.append(f"\"{manufacturer_no}\"")
    elif model_no:
        parts.append(f"\"{model_no}\"")

    # Avoid generic product_type/category since they return landing pages
    # But we can keep vehicle make/model if they exist
    vehicle_make = (extra.get("vehicle_make") or "").strip()
    vehicle_model = (extra.get("vehicle_model") or "").strip()
    vehicle_year = (extra.get("vehicle_year") or "").strip()
    if vehicle_make:
        parts.append(f"\"{vehicle_make}\"")
    if vehicle_model:
        parts.append(f"\"{vehicle_model}\"")
    if vehicle_year:
        parts.append(f"\"{vehicle_year}\"")

    # Add keyword to bias Google to product detail pages
    parts.append("site:.com \"buy\" OR \"product\"")

    return " ".join(parts)



def extract_urls(walmart_extracted: dict):
    """Search Google via Serper and return Walmart, brand product page, brand homepage, and top 5 competitors (excluding brand product page & Walmart)."""
    brand = walmart_extracted["extra"].get("brand", "")
    strict_query = build_unique_query(walmart_extracted)

    conn = http.client.HTTPSConnection("google.serper.dev")
    headers = {"X-API-KEY": SERPER_API_KEY, "Content-Type": "application/json"}

    def run_search(q: str):
        payload = json.dumps({"q": q, "gl": "us", "hl": "en"})
        conn.request("POST", "/search", payload, headers)
        res = conn.getresponse()
        raw = res.read().decode("utf-8") or "{}"

        try:
            data = json.loads(raw)
        except json.JSONDecodeError:
            logging.error(f"❌ Invalid JSON response: {raw}")
            return {}

        # Debug: log raw response if error
        if res.status != 200:
            logging.error(f"❌ Serper API error (HTTP {res.status}): {data}")
            return {}

        return data

    def is_brand_url(link: str, brand: str) -> bool:
        try:
            parsed = urlparse(link)
            hostname = (parsed.hostname or "").lower()
            if hostname.startswith("www."):
                hostname = hostname[4:]
            brand_words = re.findall(r"[a-z0-9]+", brand.lower())
            if all(word in hostname for word in brand_words):
                return True
            if brand_words and brand_words[0] in hostname:
                return True
            return False
        except Exception:
            return False

    brand_query = f"site:{brand.lower()}.com {strict_query}"
    results = run_search(brand_query)
    
    if results.get("statusCode") == 400 and "credits" in results.get("message", "").lower():
        logging.error("❌ Search API credits exhausted. Falling back.")
        

    top10 = [{"title": i.get("title"), "link": i.get("link")}
             for i in results.get("organic", [])[:20]]

    # 2️⃣ Fallback query if no results
    if not top10:
        results = run_search(strict_query)
        top10 = [{"title": i.get("title"), "link": i.get("link")}
                 for i in results.get("organic", [])[:20]]

    # Filter category/store pages
    top10 = filter_product_pages(top10)

    # Initialize
    walmart_url, brand_url, brand_page = None, None, None

    # Identify Walmart and brand URLs
    for item in top10:
        link = item.get("link", "")
        if not link:
            continue
        low = link.lower()

        if "walmart.com" in low and not walmart_url:
            walmart_url = link
            continue

        if is_brand_url(link, brand):
            parsed = urlparse(link)
            if parsed.path not in ["", "/"] and not brand_url:
                brand_url = link  # product page
            elif parsed.path in ["", "/"] and not brand_page:
                brand_page = link  # homepage

    # 2nd chance: force brand + product identifiers if brand product page missing
    if not brand_url:
        product_name = walmart_extracted["attributes"].get("product_name", "")
        mpn = walmart_extracted["extra"].get("mpn", "")
        product_identifier = f"\"{mpn or product_name}\""
        if product_identifier.strip('"'):
            brand_force_query = f"site:{brand.lower()}.com {product_identifier}"
            results = run_search(brand_force_query)
            for i in results.get("organic", []):
                link = i.get("link", "")
                if is_brand_url(link, brand):
                    parsed = urlparse(link)
                    if parsed.path not in ["", "/"]:
                        brand_url = link
                        break

    # Build competitors (exclude brand_url & walmart)
    competitors = []
    added_domains = set()

    # Add brand homepage first
    if brand_page:
        competitors.append(brand_page)
        added_domains.add(get_domain_name(brand_page, brand))

    # Add unique competitor domains (excluding brand_url & Walmart)
    for item in top10:
        link = item["link"]
        if link == brand_url:
            continue  # skip brand product page
        if "walmart.com" in link.lower():
            continue  # skip Walmart
        dom = get_domain_name(link, brand)
        if dom not in added_domains:
            competitors.append(link)
            added_domains.add(dom)
        if len(competitors) >= 5:
            break

    # Logging
    print("\n🔎 Google Query Used:", strict_query)
    print("\n🔎 Top 10 Results (filtered):")
    for i, item in enumerate(top10, start=1):
        print(f"{i}. {item['title']} - {item['link']}")

    print("\n✅ Competitor URLs (excluding brand product page & Walmart):")
    for i, url in enumerate(competitors, start=1):
        print(f"{i}. {url}")

    print(f"✅ Brand product page found: {brand_url}" if brand_url else f"⚠️ No brand product page found for {brand}")
    print(f"✅ Brand homepage found: {brand_page}" if brand_page else f"⚠️ No brand homepage found for {brand}")

    return {
        "walmart_url": walmart_url,
        "brand_url": brand_url,
        "brand_page": brand_page,
        "competitors": competitors,
        "top10": top10
    }

def get_attr_value(attrs: dict, attr: str) -> str:
    """Fetch attribute value allowing for (required)/(Recommended) variations."""
    if not attrs:
        return ""

    search_keys = [
        attr,
        f"{attr} (required)",
        f"{attr} (Required)",
        f"{attr} (REQUIRED)",
        f"{attr} (recommended)",
        f"{attr} (Recommended)",
        f"{attr} (RECOMMENDED)",
    ]

    for key in search_keys:
        if key in attrs and attrs[key]:
            return attrs[key].strip()

    return ""

def build_llm_suggested_json(product: dict) -> dict:
    """
    Build llm_suggested JSON for a product.
    Priority rules:
    1. Use value from Brand if empty in product
    2. Use value from known sellers in competitors (Amazon, Flipkart, eBay, Shopify)
    3. Fallback to first competitor / top web source
    """

    llm_suggested = {}
    known_sellers = ["amazon", "shopify", "ebay", "flipkart"]

    walmart_attrs = product.get("walmart", {}).get("attributes", {})
    brand_attrs = product.get("brand", {}).get("attributes", {})
    competitors = product.get("competitors", [])

    # Iterate over all attributes present in Walmart or product attributes
    all_attrs = set(list(walmart_attrs.keys()) + list(brand_attrs.keys()))
    
    for attr in all_attrs:
        value = walmart_attrs.get(attr)  # current product value
        source_url = product.get("walmart", {}).get("url")

        # print(f"\n🔎 Checking attribute: {attr}")
        # print(f"   Initial Walmart value: {value}")

        # 1️⃣ Priority to Brand
        if not value or value == "" or value is None:
            brand_value = brand_attrs.get(attr)
            if brand_value not in (None, ""):
                value = brand_value
                source_url = product.get("brand", {}).get("url")
                print(f"   ✅ Filled from Brand → {value}")

        # 2️⃣ Priority to known seller competitors
        if not value or value == "" or value is None:
            for seller in known_sellers:
                for comp in competitors:
                    comp_attrs = comp.get("attributes", {})
                    comp_value = comp_attrs.get(attr)
                    if comp_value not in (None, ""):
                        domain = comp.get("domain", "") or comp.get("url", "")
                        if seller in domain.lower():
                            value = comp_value
                            source_url = comp.get("url")
                            print(f"   ✅ Filled from known seller ({seller}) → {value}")
                            break
                if value not in (None, ""):
                    break

        # 3️⃣ Fallback to first competitor
        if not value or value == "" or value is None:
            for comp in competitors:
                comp_attrs = comp.get("attributes", {})
                comp_value = comp_attrs.get(attr)
                if comp_value not in (None, ""):
                    value = comp_value
                    source_url = comp.get("url")
                    print(f"   ⚠️ Filled from first competitor → {value}")
                    break

        # 4️⃣ If still nothing → NULL
        if not value or value == "" or value is None:
            value = "NULL"
            source_url = "NULL"
            # print(f"   ❌ No value found → NULL")

        llm_suggested[attr] = {
            "value": value,
            "source_url": source_url
        }

        # print(f"➡️ Final choice for {attr}: {value} (Source: {source_url})")

    return llm_suggested


def build_comparison_table(schema, walmart_extracted, brand_extracted, competitor_results):
    product_type = list(schema.get(DOMAIN, {}).keys())[0]
    attrs = [a.get("attribute_name") for a in schema[DOMAIN][product_type]["attributes"]]
    extra_fields = ["brand", "mpn", "product_type", "vehicle_make", "vehicle_model", "vehicle_year", "query"]

    rows = []

    # ✅ First row: URLs for each source
    url_row = {"Attribute": "Source URL"}
    url_row["Walmart"] = walmart_extracted.get("url", "")
    url_row["Brand"] = brand_extracted.get("url", "") if brand_extracted else ""
    brand = (walmart_extracted.get("extra") or {}).get("brand", "")

    for comp in competitor_results:
        comp_name = get_domain_name(comp.get("url", ""), brand)
        url_row[comp_name] = comp.get("url", "")

    rows.append(url_row)

    # ✅ Then attribute rows
    for attr in attrs + extra_fields:
        row = {"Attribute": attr}
        row["Walmart"] = (walmart_extracted.get("attributes") or {}).get(attr) or (walmart_extracted.get("extra") or {}).get(attr, "")
        row["Brand"] = (brand_extracted.get("attributes") or {}).get(attr) or (brand_extracted.get("extra") or {}).get(attr, "") if brand_extracted else ""
        for comp in competitor_results:
            comp_name = get_domain_name(comp.get("url", ""), brand)
            row[comp_name] = (comp.get("attributes") or {}).get(attr) or (comp.get("extra") or {}).get(attr, "")
        rows.append(row)

    return pd.DataFrame(rows)

FOLDER_NAME = "original2"  # replace with your folder in the bucket

def extract_product_id(url: str) -> str:
    """
    Extract a product ID from the Walmart URL.
    Example: 'http://walmart.com/ip/840996865' -> '840996865'
    """
    try:
        return url.rstrip("/").split("/")[-1]
    except Exception as e:
        logging.error(f"Failed to extract product ID from {url}: {e}")
        return url  # fallback to using full URL as ID

def check_bucket_for_data(url: str):
    """
    Check if Walmart URL data is already stored in GCS across multiple thread JSON files.
    Returns the cached JSON for the product if available and valid, else None.
    """
    try:
        storage_client = storage.Client()
        bucket = storage_client.bucket("wal_test_bucket")

        # List all thread blobs under "original/"
        blobs = bucket.list_blobs(prefix="original2/")
        thread_files = [b for b in blobs if b.name.endswith(".json")]

        logging.info(f"🔍 Checking {len(thread_files)} thread files for {url}")

        for blob in thread_files:
            logging.info(f"📂 Scanning blob: {blob.name}")

            try:
                with blob.open("r") as f:
                    cached_list = json.load(f)

                # Ensure it's a list of products
                if isinstance(cached_list, list):
                    for product in cached_list:
                        walmart_url = product.get("walmart", {}).get("url")
                        if walmart_url == url:
                            logging.info(f"✅ Cache hit in {blob.name} for {url}")

                            # Save locally for debugging
                            # with open("matched_product.json", "w") as f:
                            #     json.dump(product, f, indent=4)

                            return product
            except Exception as inner_e:
                logging.error(f"⚠️ Error reading {blob.name}: {inner_e}")
                continue

        logging.info(f"❌ No cache found for {url}")
        return None

    except Exception as e:
        logging.error(f"GCS check failed: {e}")
        return None



@router.post("/")
async def query(
    prompt: str = Form(...),
    model: str = Form(...),
    use_grounding: Optional[bool] = Form(False),
    context: Optional[str] = Form(None),
    url: Optional[str] = Form(None, alias="website_link")
):
    try:
        logging.info(f"API /query called with:")
        logging.info(f"  prompt: {prompt}")
        logging.info(f"  model: {model}")
        logging.info(f"  use_grounding: {use_grounding}")
        logging.info(f"  context: {context}")
        logging.info(f"  url: {url}")

        # Step 1: Check cache if Walmart URL provided
        if url:
            cached = check_bucket_for_data(url)
            if cached:
                logging.info(f"Returning cached Walmart data for {url}")
                
                # ✅ FIXED: Return the cached product data directly, not wrapped in table_markdown
                return JSONResponse(content=cached)

        # If no cache found, return error response
        resp = {"error": f"No cache found for {url}"}
        logging.info(f"Output JSON: {resp}")
        return JSONResponse(
            status_code=404,
            content=resp
        )

    except Exception as e:
        logging.error(f"/query failed: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"⚠️ Error: {str(e)}"}
        )

# Build the absolute path to your credentials file
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
SERVICE_ACCOUNT_FILE = os.path.join(BASE_DIR, "ops-excellence.json")

# Load credentials
credentials = service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE)

# Initialize a single global GCS client
gcs_client = storage.Client(credentials=credentials, project=credentials.project_id)

def append_result_to_thread(result, bucket_name, thread_id, folder="sept17"):
    """Append one URL result to thread's JSON file in GCS."""
    bucket = gcs_client.bucket(bucket_name)
    blob_name = f"{folder}/thread{thread_id}.json"
    blob = bucket.blob(blob_name)

    # Read existing JSON if exists
    if blob.exists():
        data = json.loads(blob.download_as_text())
    else:
        data = []

    # Append new result
    data.append(result)

    # Write back to GCS
    blob.upload_from_string(
        json.dumps(data, indent=2),
        content_type="application/json"
    )
    logging.info(f"✅ Appended result to gs://{bucket_name}/{blob_name}")
    return blob_name


def chunkify(df, num_chunks):
    """Split DataFrame into nearly equal chunks, last chunk takes leftovers."""
    n = len(df)
    chunk_size = n // num_chunks
    remainder = n % num_chunks

    chunks = []
    start = 0
    for i in range(num_chunks):
        end = start + chunk_size
        if i == num_chunks - 1:  # last chunk gets remainder
            end += remainder
        chunks.append(df.iloc[start:end])
        start = end
    return chunks


@router.post("/run")
def run_extraction(req: dict):
    """
    Updated to handle direct row data from AdminPage
    """
    try:
        logging.info(f"🚀 Starting extraction with request: {req}")
        
        # ✅ Handle both CSV URL and direct rows data
        if "google_sheet_url" in req:
            # Original CSV processing
            logging.info("📊 Processing CSV from URL")
            df_input = pd.read_csv(
                req["google_sheet_url"],
                engine="python",
                quotechar='"',
                on_bad_lines="skip"
            )
            df_input.columns = df_input.columns.str.strip()
        elif "rows" in req:
            # ✅ NEW: Handle direct rows data from AdminPage
            logging.info("📋 Processing direct rows data")
            rows_data = req["rows"]
            if not rows_data:
                raise HTTPException(status_code=400, detail="No rows data provided")
            
            # Convert rows to DataFrame
            df_input = pd.DataFrame(rows_data)
            logging.info(f"📊 Created DataFrame with {len(df_input)} rows and columns: {df_input.columns.tolist()}")
        else:
            raise HTTPException(status_code=400, detail="Either 'google_sheet_url' or 'rows' must be provided")

        schema = load_schema(SCHEMA_FILE)
        DOMAIN = "Vehicle"

        # --- Only keep required attributes ---
        existing_attrs = [col for col in required_attributes if col in df_input.columns]
        df_required = df_input[existing_attrs]
        print("✅ Required attributes filtered:", df_required.columns.tolist())

        all_results = []

        required_keys = df_required.columns.tolist()

        def process_row(row, thread_id):
            walmart_url = str(row.get("Website Link", "") or "")
            category = str(row.get("Product Type", "") or "")
            product_type_candidate = str(row.get("Product Name", "") or "")
            

            product_type = pick_product_type(schema, DOMAIN, [product_type_candidate])
            meta, attr_map, attr_instructions = build_attr_instructions(schema, DOMAIN, product_type)

            if not product_type:
                print(f"⚠️ Product type not found for {category} / {product_type_candidate}")
                return None

            # Load attributes safely
            try:
                data = safe_json_loads(row.get("Required Attributes", "{}"))
            except Exception:
                data = {"attributes": {}, "extra": {}}

            extracted_attrs = data.get("attributes") or {}
            extras = data.get("extra") or {}

            # Fill missing attributes from the row itself
            for attr in required_keys:
                extracted_attrs.setdefault(attr, str(row.get(attr, "") or ""))

            for k in ["brand", "mpn", "product_type", "vehicle_make", "vehicle_model", "vehicle_year", "query"]:
                extras.setdefault(k, str(row.get(k, "") or ""))

            extras["brand"] = extracted_attrs.get("Brand", "") or extracted_attrs.get("Manufacturer", "") or extras.get("brand", "")
            extras["mpn"] = extracted_attrs.get("Manufacturer Part Number", "") or extracted_attrs.get("Model", "") or extras.get("mpn", "")
            extras["product_type"] = extracted_attrs.get("Product Type", "") or extras.get("product_type", "")

            walmart_extracted = {"url": walmart_url, "attributes": extracted_attrs, "extra": extras}

            # --- Extract URLs safely ---
            try:
                
                urls = extract_urls(walmart_extracted) or {}
            except Exception as e:
                print(f"⚠️ Failed to extract URLs for {walmart_url}: {e}")
                urls = {}

            brand_extracted = {"url": None, "attributes": {k: "" for k in required_keys}, "extra": {}}
            brand_url = urls.get("brand_url")
            if brand_url:
                try:
                    brand_extracted = extract_all_schema_attributes(
                            brand_url, schema, DOMAIN, product_type, required_keys
                        ) or brand_extracted
                except Exception as e:
                        print(f"⚠️ Failed to extract brand URL {brand_url}: {e}")

            competitor_results = []
            competitor_urls = urls.get("competitors") or []
             
            for url in competitor_urls:
                if not url:
                    continue
                try:
                    competitor_results.append(
                        extract_all_schema_attributes(url, schema, DOMAIN, product_type, required_keys)
                        or {"url": url, "attributes": {k: "" for k in required_keys}, "extra": {}}
                    )
                except Exception as e:
                    print(f"⚠️ Failed to extract competitor URL {url}: {e}")
                    competitor_results.append({"url": url, "attributes": {k: "" for k in required_keys}, "extra": {}})

            result = {
                "category": category,
                "product_type": product_type,
                "walmart": walmart_extracted,
                "brand": brand_extracted,
                "competitors": competitor_results,
                "status": "pending",
                "assigned_to": None,
                "attr_map": attr_map
            }

            try:
                result["llm_suggested"] = build_llm_suggested_json(result)
            except Exception as e:
                print(f"⚠️ Failed to build LLM suggested JSON: {e}")
                result["llm_suggested"] = {}

            # Save to GCS thread file
            try:
                blob_path = append_result_to_thread(result, req["bucket_name"], thread_id)
                print(f"✅ Saved URL {walmart_url} → {blob_path}")
            except Exception as e:
                print(f"⚠️ Failed to save result for {walmart_url}: {e}")

            return result


        chunks = chunkify(df_input, 5)

        def process_chunk(df_chunk, thread_id):
            results = []
            for _, row in df_chunk.iterrows():
                res = process_row(row, thread_id)
                if res:
                    results.append(res)
            return results

        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(process_chunk, chunk, thread_id)
                       for thread_id, chunk in enumerate(chunks, start=1)]

      
        all_results = []
        for future in as_completed(futures):
            try:
                all_results.extend(future.result())
            except Exception as e:
                print(f"⚠️ Error in thread: {e}")

        return {
            "status": "success",
            "processed": len(df_input),
            "saved_to": f"gs://{req['bucket_name']}/results123456/"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    
class FeedbackRequest(BaseModel):
    id_str: str
    feedback: str
    finalValues: list[dict]
    shortSummary: str | None = None
    detailedSummary: str | None = None
    rawJson: str | None = None
    wideFormatData: dict | None = None

SPREADSHEET_ID = "1PY6ZJzxz_96jwmgsiht4mTkZv6GKP3GogoTQw3iYR7U"
WIDE_SHEET_NAME = "Wide_Format_Feedback"  # only this will be used

@router.post("/feedback")
async def submit_feedback(req: FeedbackRequest):
    """
    Save feedback in wide format directly to Google Sheets
    """
    request_id = f"feedback_{int(time.time() * 1000)}"
    logger.info(f"💬 FEEDBACK ENDPOINT [{request_id}]")

    try:
        gc = get_gsheet_client()
        sh = gc.open_by_key(SPREADSHEET_ID)

        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

        # ✅ ensure wide format sheet exists
        try:
            worksheet = sh.worksheet(WIDE_SHEET_NAME)
        except:
            worksheet = sh.add_worksheet(
                title=WIDE_SHEET_NAME,
                rows=1000,
                cols=200  # enough columns
            )
            logger.info(f"✅ Created new sheet: {WIDE_SHEET_NAME}")

        # Prepare headers
        metadata_columns = [
            "Submission_ID", "Timestamp", "Feedback",
            "Short_Summary", "Detailed_Summary", "Attributes_Count"
        ]

        current_headers = worksheet.row_values(1)
        if not current_headers:
            # create headers if sheet is empty
            all_headers = metadata_columns + sorted(req.wideFormatData.keys())
            worksheet.insert_row(all_headers, 1)
            current_headers = all_headers

        # Build row
        data_row = [""] * len(current_headers)
        metadata_values = {
            "Submission_ID": req.id_str,
            "Timestamp": timestamp,
            "Feedback": req.feedback,
            "Short_Summary": req.shortSummary or "",
            "Detailed_Summary": req.detailedSummary or "",
            "Attributes_Count": str(len(req.finalValues))
        }

        for i, header in enumerate(current_headers):
            if header in metadata_values:
                data_row[i] = metadata_values[header]
            elif header in req.wideFormatData:
                data_row[i] = str(req.wideFormatData[header])

        # Append row
        worksheet.append_row(data_row, value_input_option="RAW")

        logger.info(f"✅ [{request_id}] Saved wide format row with {len(data_row)} columns")

        return {
            "status": "success",
            "message": "✅ Feedback saved to Wide_Format_Feedback",
            "saved_to_sheet": WIDE_SHEET_NAME,
            "columns": len(current_headers)
        }

    except Exception as e:
        logger.error(f"❌ [{request_id}] Save failed: {e}")
        raise HTTPException(status_code=500, detail=f"Error saving feedback: {e}")
