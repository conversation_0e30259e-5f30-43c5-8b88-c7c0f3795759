import logging
import itertools
from typing import Any, Dict, List

import pandas as pd
import numpy as np
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from google.cloud import bigquery

from config import get_bq_client, ASSIGN_TABLE_FULL

# Router
router = APIRouter()

# Logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Clients
bq_client = get_bq_client()

# Local counter (fallback auto-increment)
_id_counter = itertools.count(1)


# --- Helpers ---
def clean_value(value):
    """Normalize values before inserting into BigQuery."""
    if value is None:
        return None
    if isinstance(value, float) and pd.isna(value):
        return None
    if isinstance(value, (np.generic,)):
        return value.item()
    return value


# --- Models ---
class SaveRequest(BaseModel):
    rows: List[Dict[str, Any]]


class UpdateStatusRequest(BaseModel):
    id_str: str
    new_status: str
    username: str | None = None


# --- Routes ---
@router.post("/save")
async def save_assign(data: SaveRequest):
    try:
        rows_to_insert = []
        for r in data.rows:
            clean_rec = {k: clean_value(v) for k, v in r.items()}
            clean_rec["id"] = next(_id_counter)  # auto id
            if not clean_rec.get("assign"):
                clean_rec["assign"] = "Unassigned"
            rows_to_insert.append(clean_rec)

        if not rows_to_insert:
            raise HTTPException(status_code=400, detail="No rows to save")

        errors = bq_client.insert_rows_json(ASSIGN_TABLE_FULL, rows_to_insert)
        if errors:
            logging.error(f"BigQuery insert error: {errors}")
            raise HTTPException(status_code=500, detail=f"BigQuery insert error: {errors}")

        return {"message": "✅ Data saved successfully", "saved": len(rows_to_insert)}

    except Exception as e:
        logging.error(f"Error saving assign data: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/")
def get_assign(limit: int = 100, offset: int = 0, search: str = "", username: str = ""):
    where_clauses = []
    params = [
        bigquery.ScalarQueryParameter("limit", "INT64", limit),
        bigquery.ScalarQueryParameter("offset", "INT64", offset),
    ]

    if username:
        where_clauses.append("assign = @username")
        params.append(bigquery.ScalarQueryParameter("username", "STRING", username))

    if search:
        where_clauses.append("""
            (
                LOWER(product_name) LIKE @search OR
                LOWER(product_type) LIKE @search OR
                LOWER(category) LIKE @search OR
                CAST(item_id AS STRING) LIKE @search
            )
        """)
        params.append(bigquery.ScalarQueryParameter("search", "STRING", f"%{search.lower()}%"))

    where_clause = "WHERE " + " AND ".join(where_clauses) if where_clauses else ""

    query = f"""
        SELECT *
        FROM `{ASSIGN_TABLE_FULL}`
        {where_clause}
        ORDER BY date_yyyy_mm_dd DESC
        LIMIT @limit OFFSET @offset
    """
    try:
        results = bq_client.query(query, job_config=bigquery.QueryJobConfig(query_parameters=params)).result()
        rows = [dict(row) for row in results]

        count_query = f"SELECT COUNT(*) as total FROM `{ASSIGN_TABLE_FULL}` {where_clause}"
        count_job = bq_client.query(count_query, job_config=bigquery.QueryJobConfig(query_parameters=params))
        total_count = list(count_job.result())[0]["total"]

        return {"rows": rows, "count": total_count}

    except Exception as e:
        logging.error(f"Error fetching assign data: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/update-status")
def update_status(payload: UpdateStatusRequest):
    try:
        query = f"""
        UPDATE `{ASSIGN_TABLE_FULL}`
        SET status = @status
        WHERE id_str = @id_str
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("status", "STRING", payload.new_status),
                bigquery.ScalarQueryParameter("id_str", "STRING", payload.id_str),
            ]
        )
        bq_client.query(query, job_config=job_config).result()
        return {"message": "✅ Status updated", "id_str": payload.id_str, "new_status": payload.new_status}

    except Exception as e:
        logging.error(f"❌ Error updating status for {payload.id_str}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status-summary")
def get_status_summary(username: str = ""):
    where_clause = "WHERE assign = @username" if username else ""
    params = []
    if username:
        params.append(bigquery.ScalarQueryParameter("username", "STRING", username))

    query = f"""
    SELECT status, COUNT(*) AS count
    FROM `{ASSIGN_TABLE_FULL}`
    {where_clause}
    GROUP BY status
    """
    try:
        query_job = bq_client.query(query, job_config=bigquery.QueryJobConfig(query_parameters=params))
        results = query_job.result()

        summary = {"Pending": 0, "In Progress": 0, "Completed": 0}
        for row in results:
            status = row["status"] or "Pending"
            summary[status] = summary.get(status, 0) + row["count"]

        return summary
    except Exception as e:
        logging.error(f"❌ Error fetching status summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/users-status-summary")
def get_users_status_summary():
    query = f"""
    SELECT assign AS username, status, COUNT(*) AS count
    FROM `{ASSIGN_TABLE_FULL}`
    GROUP BY username, status
    ORDER BY username
    """
    try:
        results = bq_client.query(query).result()
        summary_dict = {}

        for row in results:
            user = row["username"] or "Unassigned"
            status = row["status"] or "Pending"
            if user not in summary_dict:
                summary_dict[user] = {"Pending": 0, "In Progress": 0, "Completed": 0}
            summary_dict[user][status] = row["count"]

        return [{"username": u, **summary_dict[u]} for u in summary_dict]
    except Exception as e:
        logging.error(f"❌ Error fetching user status summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class UpdateStatusWithTaskUrlRequest(BaseModel):
    id_str: str
    new_status: str
    task_url: str
    username: str | None = None

@router.post("/update-status-with-task-url")
def update_status_with_task_url(payload: UpdateStatusWithTaskUrlRequest):
    try:
        query = f"""
        UPDATE `{ASSIGN_TABLE_FULL}`
        SET status = @status, task_url = @task_url
        WHERE id_str = @id_str
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("status", "STRING", payload.new_status),
                bigquery.ScalarQueryParameter("task_url", "STRING", payload.task_url),
                bigquery.ScalarQueryParameter("id_str", "STRING", payload.id_str),
            ]
        )
        bq_client.query(query, job_config=job_config).result()
        return {
            "message": "✅ Status and task URL updated", 
            "id_str": payload.id_str, 
            "new_status": payload.new_status,
            "task_url": payload.task_url
        }

    except Exception as e:
        logging.error(f"❌ Error updating status and task URL for {payload.id_str}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
