# My Fullstack App - Backend

## Overview

This is the backend for the My Fullstack App, built using FastAPI. It provides endpoints for file uploads and querying data.

## Setup

1. **Create a virtual environment**:
   ```
   python -m venv venv
   ```

2. **Activate the virtual environment**:
   - On Windows:
     ```
     venv\Scripts\activate
     ```
   - On macOS/Linux:
     ```
     source venv/bin/activate
     ```

3. **Install dependencies**:
   ```
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```
   uvicorn app.main:app --reload
   ```

## Endpoints

- **POST /upload**
  - Upload a file and receive a response with columns and preview data.

- **POST /query**
  - Send a prompt and receive a streaming response based on the model and grounding options.

## Environment Variables

Make sure to set up your environment variables in the `.env` file as needed.

## Troubleshooting

- Ensure the FastAPI server is running and accessible.
- Check for any errors in the console for missing dependencies or incorrect configurations.
- Verify that the endpoints are correctly defined and accessible.