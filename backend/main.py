from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from routes import auth, assign, file_upload, query, claim, prompt, task
from routes.latestquery19Sept import router as updatedquery

app = FastAPI(title="Walmart Backend")

# Allow CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], allow_credentials=True,
    allow_methods=["*"], allow_headers=["*"]
)

# Routers
app.include_router(auth.router, tags=["Auth"])
app.include_router(assign.router, prefix="/assign", tags=["Assign"])
app.include_router(file_upload.router, tags=["Files"])
app.include_router(query.router, prefix="/query", tags=["Gemini"])
app.include_router(claim.router, prefix="/claim", tags=["Claim"])  
app.include_router(updatedquery, prefix="/updatedquery", tags=["Updatedquery"])
app.include_router(prompt.router, prefix="/prompt", tags=["prompt"]) 
app.include_router(task.router, prefix="/api/task", tags=["task"])  

@app.get("/")
def root():
    return {"message": "FastAPI + BigQuery + Gemini is running 🚀"}

