#!/bin/bash

# Frontend deployment script for testing environment: Frontend to Cloud Run
# Run from project root (/home/<USER>/Documents/cqe-wallmart)
# Usage: ./deploy_frontend_test.sh
# Deploys to a separate service 'frontend-test-wallmart' for testing

set -e  # Exit on error

FRONTEND_DIR="frontend"
SERVICE_NAME="frontend-test-wallmart"
PROJECT_ID="ops-excellence"
LOCATION="us-central1"
REPOSITORY="walmart-docker"
IMAGE_NAME="project_walmart_frontend_test"
TAG="test-latest"

echo "🚀 Starting frontend test deployment..."

# Step 1: Deploy Frontend (Test Environment)
echo "=== Frontend Test Deployment ==="
cd ${FRONTEND_DIR}

# Authenticate Docker
echo "🔐 Authenticating Docker..."
gcloud auth configure-docker ${LOCATION}-docker.pkg.dev

# Build image
echo "🏗️ Building frontend test image..."
docker build -t ${IMAGE_NAME}:${TAG} .

# Tag for Artifact Registry
FULL_IMAGE_NAME="${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${IMAGE_NAME}:${TAG}"
docker tag ${IMAGE_NAME}:${TAG} ${FULL_IMAGE_NAME}

# Push to Artifact Registry
echo "📤 Pushing to Artifact Registry..."
docker push ${FULL_IMAGE_NAME}

# Deploy to Cloud Run (Test Service)
echo "☁️ Deploying frontend test to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
  --image ${FULL_IMAGE_NAME} \
  --platform managed \
  --region ${LOCATION} \
  --allow-unauthenticated \
  --project ${PROJECT_ID} \
  --port 80  # nginx default port

if [ $? -ne 0 ]; then
  echo "❌ Frontend test deployment failed."
  exit 1
fi

# Get frontend test URL
FRONTEND_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${LOCATION} --format='value(status.url)' --project=${PROJECT_ID})
echo "✅ Frontend test deployed at: ${FRONTEND_URL}"

cd ..

echo "🎉 Frontend test deployment complete!"
echo "Frontend Test URL: ${FRONTEND_URL}"