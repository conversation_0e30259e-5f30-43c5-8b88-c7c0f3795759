#!/bin/bash

# Backend deployment script for testing environment: Backend to Cloud Run
# Run from project root (/home/<USER>/Documents/cqe-wallmart)
# Usage: ./deploy_backend_test.sh
# Deploys to a separate service 'backend-test-wallmart' for testing

set -e  # Exit on error

BACKEND_DIR="backend"
FRONTEND_DIR="frontend"
ENV_FILE="${FRONTEND_DIR}/.env"
SERVICE_NAME="backend-test-wallmart"
PROJECT_ID="ops-excellence"
LOCATION="us-central1"
REPOSITORY="walmart-docker"
IMAGE_NAME="project_walmart_test"
TAG="test-latest"

echo "🚀 Starting backend test deployment..."

# Step 1: Deploy Backend (Test Environment)
echo "=== Backend Test Deployment ==="
cd ${BACKEND_DIR}

# Authenticate Docker
echo "🔐 Authenticating Docker..."
gcloud auth configure-docker ${LOCATION}-docker.pkg.dev

# Build image
echo "🏗️ Building backend test image..."
docker build -t ${IMAGE_NAME}:${TAG} .

# Tag for Artifact Registry
FULL_IMAGE_NAME="${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${IMAGE_NAME}:${TAG}"
docker tag ${IMAGE_NAME}:${TAG} ${FULL_IMAGE_NAME}

# Push to Artifact Registry
echo "📤 Pushing to Artifact Registry..."
docker push ${FULL_IMAGE_NAME}

# Deploy to Cloud Run (Test Service)
echo "☁️ Deploying backend test to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
  --image ${FULL_IMAGE_NAME} \
  --platform managed \
  --region ${LOCATION} \
  --allow-unauthenticated \
  --project ${PROJECT_ID} \
  --port 8080  # Explicit port for Cloud Run

if [ $? -ne 0 ]; then
  echo "❌ Backend test deployment failed."
  exit 1
fi

# Get backend test URL
BACKEND_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${LOCATION} --format='value(status.url)' --project=${PROJECT_ID})
echo "✅ Backend test deployed at: ${BACKEND_URL}"

cd ..

# Step 2: Update Frontend .env with Backend Test URL (for local dev or future frontend deploys)
echo "=== Updating Frontend Config for Test ==="
if [ -f "${ENV_FILE}" ]; then
  sed -i "s|^VITE_API_BASE_URL=.*|VITE_API_BASE_URL=${BACKEND_URL}|" ${ENV_FILE}
else
  echo "VITE_API_BASE_URL=${BACKEND_URL}" > ${ENV_FILE}
fi
echo "✅ Updated ${ENV_FILE} with backend test URL: ${BACKEND_URL}"

echo "🎉 Backend test deployment complete!"
echo "Backend Test URL: ${BACKEND_URL}"