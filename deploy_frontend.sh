#!/bin/bash

# Frontend deployment script: Frontend to Cloud Run
# Run from project root (/home/<USER>/Documents/cqe-wallmart)
# Usage: ./deploy_frontend.sh
# Note: Ensure backend is deployed first to set VITE_API_BASE_URL in frontend/.env

set -e  # Exit on error

FRONTEND_DIR="frontend"
ENV_FILE="${FRONTEND_DIR}/.env"
FRONTEND_SERVICE_NAME="cqe-walmart"
PROJECT_ID="ops-excellence"
LOCATION="us-central1"
REPOSITORY="walmart-docker"
FRONTEND_IMAGE_NAME="project_walmart_frontend"
TAG="latest"

echo "🚀 Starting frontend deployment..."

# Step 1: Read Backend URL from .env
echo "=== Reading Backend Config ==="
if [ -f "${ENV_FILE}" ]; then
  BACKEND_URL=$(grep "^VITE_API_BASE_URL=" ${ENV_FILE} | cut -d'=' -f2-)
  if [ -z "${BACKEND_URL}" ]; then
    echo "❌ VITE_API_BASE_URL not found in ${ENV_FILE}. Run deploy_backend.sh first."
    exit 1
  fi
  echo "✅ Found backend URL: ${BACKEND_URL}"
else
  echo "❌ ${ENV_FILE} not found. Run deploy_backend.sh first to create it."
  exit 1
fi

# Step 2: Deploy Frontend
echo "=== Frontend Deployment ==="
cd ${FRONTEND_DIR}

# Authenticate Docker
echo "🔐 Authenticating Docker..."
gcloud auth configure-docker ${LOCATION}-docker.pkg.dev

# Build image with backend URL as build arg
echo "🏗️ Building frontend image..."
docker build --no-cache --build-arg VITE_API_BASE_URL=${BACKEND_URL} -t ${FRONTEND_IMAGE_NAME}:${TAG} .

# Tag for Artifact Registry
FULL_IMAGE_NAME="${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${FRONTEND_IMAGE_NAME}:${TAG}"
docker tag ${FRONTEND_IMAGE_NAME}:${TAG} ${FULL_IMAGE_NAME}

# Push to Artifact Registry
echo "📤 Pushing to Artifact Registry..."
docker push ${FULL_IMAGE_NAME}

# Deploy to Cloud Run
echo "☁️ Deploying frontend to Cloud Run..."
gcloud run deploy ${FRONTEND_SERVICE_NAME} \
  --image ${FULL_IMAGE_NAME} \
  --platform managed \
  --region ${LOCATION} \
  --allow-unauthenticated \
  --project ${PROJECT_ID} \
  --port 80

if [ $? -ne 0 ]; then
  echo "❌ Frontend deployment failed."
  exit 1
fi

# Get frontend URL
FRONTEND_URL=$(gcloud run services describe ${FRONTEND_SERVICE_NAME} --region=${LOCATION} --format='value(status.url)' --project=${PROJECT_ID})
echo "✅ Frontend deployed at: ${FRONTEND_URL}"

cd ..

echo "🎉 Frontend deployment complete!"
echo "Frontend URL: ${FRONTEND_URL}"