#!/bin/bash

# Fullstack deployment script: Backend to Cloud Run + Frontend to Vercel/Netlify
# Run from project root (/home/<USER>/Documents/my-fullstack-app)
# Usage: ./deploy.sh

set -e  # Exit on error

BACKEND_DIR="backend"
FRONTEND_DIR="frontend"
ENV_FILE="${FRONTEND_DIR}/.env"
SERVICE_NAME="walmart-backend"
FRONTEND_SERVICE_NAME="cqe-walmart"
PROJECT_ID="ops-excellence"
LOCATION="us-central1"
REPOSITORY="walmart-docker"
IMAGE_NAME="project_walmart"
FRONTEND_IMAGE_NAME="project_walmart_frontend"
TAG="latest"

echo "🚀 Starting fullstack deployment..."

# Step 1: Deploy Backend
echo "=== Backend Deployment ==="
cd ${BACKEND_DIR}

# Authenticate Docker
echo "🔐 Authenticating Docker..."
gcloud auth configure-docker ${LOCATION}-docker.pkg.dev

# Build image
echo "🏗️ Building backend image..."
docker build -t ${IMAGE_NAME}:${TAG} .

# Tag for Artifact Registry
FULL_IMAGE_NAME="${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${IMAGE_NAME}:${TAG}"
docker tag ${IMAGE_NAME}:${TAG} ${FULL_IMAGE_NAME}

# Push to Artifact Registry
echo "📤 Pushing to Artifact Registry..."
docker push ${FULL_IMAGE_NAME}

# Deploy to Cloud Run
echo "☁️ Deploying backend to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
  --image ${FULL_IMAGE_NAME} \
  --platform managed \
  --region ${LOCATION} \
  --allow-unauthenticated \
  --project ${PROJECT_ID} \
  --port 8080  # Explicit port for Cloud Run

if [ $? -ne 0 ]; then
  echo "❌ Backend deployment failed."
  exit 1
fi

# Get backend URL
BACKEND_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${LOCATION} --format='value(status.url)' --project=${PROJECT_ID})
echo "✅ Backend deployed at: ${BACKEND_URL}"

cd ..

# Step 2: Update Frontend .env with Backend URL (for local dev)
echo "=== Updating Frontend Config ==="
if [ -f "${ENV_FILE}" ]; then
  sed -i "s|^VITE_API_BASE_URL=.*|VITE_API_BASE_URL=${BACKEND_URL}|" ${ENV_FILE}
else
  echo "VITE_API_BASE_URL=${BACKEND_URL}" > ${ENV_FILE}
fi
echo "✅ Updated ${ENV_FILE} with backend URL: ${BACKEND_URL}"

# Step 3: Deploy Frontend
echo "=== Frontend Deployment ==="
cd ${FRONTEND_DIR}

# Authenticate Docker
echo "🔐 Authenticating Docker..."
gcloud auth configure-docker ${LOCATION}-docker.pkg.dev

# Build image
echo "🏗️ Building frontend image..."
docker build --no-cache --build-arg VITE_API_BASE_URL=${BACKEND_URL} -t ${FRONTEND_IMAGE_NAME}:${TAG} .

# Tag for Artifact Registry
FULL_IMAGE_NAME="${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${FRONTEND_IMAGE_NAME}:${TAG}"
docker tag ${FRONTEND_IMAGE_NAME}:${TAG} ${FULL_IMAGE_NAME}

# Push to Artifact Registry
echo "📤 Pushing to Artifact Registry..."
docker push ${FULL_IMAGE_NAME}

# Deploy to Cloud Run
echo "☁️ Deploying frontend to Cloud Run..."
gcloud run deploy ${FRONTEND_SERVICE_NAME} \
  --image ${FULL_IMAGE_NAME} \
  --platform managed \
  --region ${LOCATION} \
  --allow-unauthenticated \
  --project ${PROJECT_ID} \
  --port 80

if [ $? -ne 0 ]; then
  echo "❌ Frontend deployment failed."
  exit 1
fi

# Get frontend URL
FRONTEND_URL=$(gcloud run services describe ${FRONTEND_SERVICE_NAME} --region=${LOCATION} --format='value(status.url)' --project=${PROJECT_ID})
echo "✅ Frontend deployed at: ${FRONTEND_URL}"

cd ..

echo "🎉 Fullstack deployment complete!"
echo "Backend: ${BACKEND_URL}"
echo "Frontend: ${FRONTEND_URL}"
