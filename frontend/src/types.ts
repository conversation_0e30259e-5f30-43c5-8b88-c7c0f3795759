export type Role = "admin" | "user";

export interface User {
  id: string;
  username: string;
  email: string;
  role: "admin" | "user";
}

export interface Task {
  id: string;
  name: string;
  details?: string;
  assignedTo?: string;
  status: "pending" | "in-progress" | "done";
}


export interface PreviewRow {
  [key: string]: string | number | null;
}

export interface UploadResponse {
  columns: string[];
  preview: PreviewRow[];
  googleSheetUrl: string;
}

export interface WalmartTask {
  url: string;
  assignee: string;
}

export interface StreamChunk {
  text: string;
}

export interface ChatMessage {
  role: "user" | "assistant";
  content: string;
  timestamp: number;
  tokens?: number;
}

export interface TaskWithAssignment {
  id: string;
  name: string; // <-- REQUIRED for UI table rows
  url?: string;
  assignedTo?: string;
  status?: "pending" | "in-progress" | "done";
}