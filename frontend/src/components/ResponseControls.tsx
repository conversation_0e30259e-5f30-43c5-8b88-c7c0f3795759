import React from 'react';
import { Button } from './ui/button';
import { Loader2, RefreshCcw } from 'lucide-react';

interface Props {
  preloadedRow: any;
  loading: boolean;
  fetchResponse: () => void;
  exportToWideFormatCSV?: () => void;
  generateInsights?: () => void;
  clearSelection?: () => void;
}

const ResponseControls: React.FC<Props> = ({ preloadedRow, loading, fetchResponse, clearSelection }) => {
  return (
    <div>
      {preloadedRow && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b border-gray-200 dark:border-gray-700 p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Product Analysis</h2>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Analyzing data for: <a href={preloadedRow.website_link} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline font-medium hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 break-all">{preloadedRow.website_link || 'Product'}</a></p>
              <p className="text-sm text-gray-600 dark:text-gray-400">{preloadedRow.product_name ? `Product Name: ${preloadedRow.product_name}` : ''}{preloadedRow.product_type ? ` | Product Type: ${preloadedRow.product_type}` : ''}{preloadedRow.category ? ` | Category: ${preloadedRow.category}` : ''}{preloadedRow.product_id ? ` | GTIN: ${preloadedRow.product_id}` : ''}{preloadedRow.item_id ? ` (ID: ${preloadedRow.item_id})` : ''}</p>
            </div>

            <div className="flex gap-3 flex-shrink-0">
              <Button size="sm" variant="outline" className="flex items-center gap-2 rounded-xl border hover:shadow-lg transition-all" onClick={fetchResponse} disabled={loading}>
                {loading ? <Loader2 className="w-4 h-4 animate-spin" /> : <RefreshCcw className="w-4 h-4" />} Refresh Data
              </Button>
              {clearSelection && (
                <Button size="sm" variant="outline" className="flex items-center gap-2 rounded-xl" onClick={clearSelection}>
                  Clear Selection
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Action buttons shown below table are handled by parent; this component only renders top header */}
    </div>
  );
};

export default ResponseControls;

