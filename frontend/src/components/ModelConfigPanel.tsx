import { useState } from "react";
import { But<PERSON> } from "./ui/button";
import ModelSelect from "./ModelSelect";
import GroundingToggle from "./GroundingToggle";
import { Settings2, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

export default function ModelConfigPanel() {
  const [open, setOpen] = useState(false);

  return (
    <>
      {/* Floating Toggle Button */}
      <div className="fixed top-2 left-2 z-50">
        <Button
          variant="default"
          className="flex items-center gap-2 shadow-lg bg-gradient-to-r from-blue-400 to-indigo-500 text-white hover:from-blue-500 hover:to-indigo-600"
          onClick={() => setOpen(true)}
        >
          <Settings2 className="w-4 h-4" /> Settings
        </Button>
      </div>

      <AnimatePresence>
        {open && (
          <>
            {/* Overlay */}
            <motion.div
              className="fixed inset-0 bg-black/30 z-40"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setOpen(false)}
            />

            {/* Sidebar */}
            <motion.div
              className="fixed left-0 top-0 h-full w-80 bg-white z-50 shadow-2xl p-6 flex flex-col overflow-y-auto"
              initial={{ x: "-100%" }}
              animate={{ x: 0 }}
              exit={{ x: "-100%" }}
              transition={{ type: "tween", duration: 0.3 }}
            >
              <div className="flex items-center justify-between mb-6 border-b pb-3">
                <h2 className="text-lg font-bold text-gray-800 flex items-center gap-2">
                  <Settings2 className="w-5 h-5 text-blue-600" /> Model Config
                </h2>
                <Button
                  variant="ghost"
                  className="text-gray-500 hover:text-gray-700"
                  onClick={() => setOpen(false)}
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>

              <div className="space-y-6">
                <div className="z-50">
                  <span className="text-sm font-medium text-gray-700 mb-1 block">Model:</span>
                  <div className="relative z-50">
                    <ModelSelect />
                  </div>
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-700 mb-1 block">Grounding:</span>
                  <GroundingToggle />
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}
