const seg = typeof Intl !== 'undefined' && 'Segmenter' in Intl
  ? new Intl.Segmenter('en', { granularity: 'sentence' })
  : null;

function normalizeWhitespace(text: string): string {
  return (text ?? '').replace(/\s+/g, ' ').trim();
}

function ensureTerminalPunct(s: string): string {
  return /[.!?]$/.test(s) ? s : s + '.';
}

export function formatSummary(
  text: string,
  opts: { mode: 'short' | 'list' } = { mode: 'short' }
): string | string[] {
  const t = normalizeWhitespace(text);
  const rawSegments = seg
    ? Array.from(seg.segment(t)).map(({ segment }) => segment)
    : (t.match(/[^.!?]+[.!?]*/g) || []); // fallback
  const sentences = rawSegments
    .map(s => s.trim())
    .filter(Boolean)
    .map(ensureTerminalPunct);

  return opts.mode === 'list' ? sentences : sentences.join(' ');
}
