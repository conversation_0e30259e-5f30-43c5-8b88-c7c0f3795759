import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type SortingState,
  type PaginationState,
} from "@tanstack/react-table";
import { useState, useMemo } from "react";
import type { PreviewRow } from "../types";
import { Button } from "./ui/button";
import { Input } from "./ui/input";

interface Props {
  data: PreviewRow[];
  columns: string[];
  loading?: boolean;
}

export default function DataTable({ data, columns, loading = false }: Props) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // Convert column names to react-table ColumnDefs
  const tableColumns: ColumnDef<PreviewRow>[] = useMemo(
    () =>
      columns.map((col) => ({
        accessorKey: col,
        header: col,
        cell: (info) => {
          const value = String(info.getValue() ?? "");
          return (
            <div className="max-w-xs truncate" title={value}>
              {value}
            </div>
          );
        },
      })),
    [columns]
  );

  // Filter data by global filter
  const filteredData = useMemo(
    () =>
      data.filter((row) =>
        columns.some((col) =>
          String(row[col] ?? "").toLowerCase().includes(globalFilter.toLowerCase())
        )
      ),
    [data, columns, globalFilter]
  );

  const table = useReactTable({
    data: filteredData,
    columns: tableColumns,
    state: {
      sorting,
      pagination,
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return (
    <div className="mt-4">
      {/* Global search */}
      <div className="mb-2 flex justify-end">
        <Input
          type="text"
          placeholder="Search..."
          value={globalFilter}
          onChange={(e) => setGlobalFilter(e.target.value)}
          className="w-full sm:w-64"
        />
      </div>

      {/* Scrollable table */}
      <div className="overflow-x-auto rounded-lg shadow border">
        <table className="min-w-full border text-sm">
          <thead className="bg-gray-100 sticky top-0 z-10">
            {table.getHeaderGroups().map((hg) => (
              <tr key={hg.id}>
                {hg.headers.map((h) => (
                  <th
                    key={h.id}
                    className="px-3 py-2 border text-left font-medium text-gray-700 cursor-pointer select-none"
                    onClick={h.column.getToggleSortingHandler()}
                  >
                    {flexRender(h.column.columnDef.header, h.getContext())}
                    <span className="ml-1">
                      {h.column.getIsSorted() === "asc"
                        ? "🔼"
                        : h.column.getIsSorted() === "desc"
                        ? "🔽"
                        : ""}
                    </span>
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {loading
              ? Array.from({ length: 5 }).map((_, idx) => (
                  <tr key={idx} className="animate-pulse bg-gray-50">
                    {columns.map((col) => (
                      <td key={col} className="px-3 py-2 border whitespace-nowrap">
                        <div className="h-4 bg-gray-200 rounded w-full"></div>
                      </td>
                    ))}
                  </tr>
                ))
              : table.getRowModel().rows.map((row) => (
                  <tr key={row.id} className="hover:bg-gray-50">
                    {row.getVisibleCells().map((cell) => (
                      <td
                        key={cell.id}
                        className="px-3 py-2 border whitespace-nowrap max-w-xs truncate"
                        title={String(cell.getValue() ?? "")}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
          </tbody>
        </table>
      </div>

      {/* Pagination controls */}
      <div className="flex items-center gap-2 mt-2 flex-wrap">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Prev
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Next
        </Button>
        <span className="text-xs text-gray-500">
          Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
        </span>
        <span className="text-xs text-gray-500 ml-auto">
          Rows per page:
          <select
            className="ml-1 border rounded px-1 py-0.5"
            value={table.getState().pagination.pageSize}
            onChange={(e) => table.setPageSize(Number(e.target.value))}
          >
            {[5, 10, 20, 50].map((size) => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
        </span>
      </div>
    </div>
  );
}
