import { useState, useEffect } from "react";
import { ShoppingCart, Menu, X, User, <PERSON>, Sun } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAppStore } from "../store/app";

type UserData = {
  username: string;
  email: string;
  role: string;
};

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const role = useAppStore((s) => s.role);
  const userId = useAppStore((s) => s.userId);
  const logout = useAppStore((s) => s.logout);
  const navigate = useNavigate();

  // Load user info from Zustand or fallback localStorage (customize as needed)
  const [user, setUser] = useState<UserData | null>(null);

  useEffect(() => {
    // If using Zustand user info, update here accordingly:
    // Example: map userId and role to username/email if needed

    // Alternatively, load from localStorage or API:
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch {
        localStorage.removeItem("user");
      }
    } else if (userId && role) {
      // Simple mock user object if not in localStorage
      setUser({
        username: userId,
        email: `${userId}@example.com`,
        role: role,
      });
    }
  }, [userId, role]);

  // Toggle dark mode class on <html>
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [darkMode]);

  // Handle logout: clear store, localStorage, and navigate to login
  const handleLogout = () => {
    logout(); // Zustand logout
    localStorage.removeItem("user"); // Clear any user localStorage if used
    setUser(null);
    setShowProfile(false);
    navigate("/login");
  };

  return (
    <>
      {/* Brand */}
      <div className="fixed left-0 top-0 z-50 flex items-center px-6 py-3 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 w-full">
        <div className="flex items-center gap-2 text-xl font-extrabold text-gray-800 dark:text-gray-100 cursor-pointer select-none">
          <ShoppingCart className="h-6 w-6 text-blue-500 animate-bounce" />
          <span className="bg-gradient-to-r from-blue-500 to-indigo-500 bg-clip-text text-transparent hover:scale-105 transition-transform">
            Walmart Assistant
          </span>
        </div>

        {/* Spacer */}
        <div className="flex-grow"></div>

        {/* Desktop Controls */}
        <div className="hidden md:flex items-center gap-3">
          {/* Dark Mode Toggle */}
          <button
            aria-label="Toggle Dark Mode"
            className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition"
            onClick={() => setDarkMode(!darkMode)}
          >
            {darkMode ? (
              <Sun className="h-5 w-5 text-yellow-400" />
            ) : (
              <Moon className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            )}
          </button>

          {/* Profile Button */}
          <button
            aria-label="Show Profile"
            className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition"
            onClick={() => setShowProfile(true)}
          >
            <User className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          </button>
        </div>

        {/* Mobile Hamburger */}
        <button
          aria-label="Toggle menu"
          className="md:hidden p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <X /> : <Menu />}
        </button>
      </div>

      {/* Profile Sidebar */}
      {showProfile && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex justify-end z-50"
          onClick={() => setShowProfile(false)} // close sidebar on backdrop click
        >
          <div
            className="w-80 bg-white dark:bg-gray-900 h-full shadow-xl p-6 relative animate-[slideInRight_0.4s_ease]"
            onClick={(e) => e.stopPropagation()} // prevent closing when clicking inside panel
          >
            <button
              aria-label="Close profile panel"
              className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition"
              onClick={() => setShowProfile(false)}
            >
              <X className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            </button>

            <div className="flex flex-col items-center mt-10 space-y-4">
              {user ? (
                <>
                  <div className="w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-4xl font-semibold text-gray-500 dark:text-gray-400 select-none">
                    {user.username.charAt(0).toUpperCase()}
                  </div>
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100 select-text">
                    {user.username}
                  </h2>
                  <p className="text-gray-500 dark:text-gray-400 text-sm break-words text-center select-text">
                    {user.email}
                  </p>
                  <span className="text-xs px-3 py-1 rounded bg-blue-100 text-blue-700 select-text">
                    {user.role}
                  </span>
                  <div className="w-full mt-6">
                    <button
                      className="w-full bg-red-500 text-white py-2 rounded-lg hover:bg-red-600 transition"
                      onClick={handleLogout}
                    >
                      Sign Out
                    </button>
                  </div>
                </>
              ) : (
                <p className="text-gray-600 dark:text-gray-300 mt-4 select-text">
                  No user logged in
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
