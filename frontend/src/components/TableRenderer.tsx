// TableRenderer.tsx
import React from "react";
import { processDoublePipe } from "@/lib/utils";
import {
  CheckSquare,
  Edit2,
  Check,
  X,
  Sparkles,
  ChevronDown,
  ExternalLink,
  MessageSquare
} from "lucide-react";

// ✅ INTERFACES
interface EnhancedFinalValue {
  Attribute?: string;
  finalValue: string;
  source: string;
  walmartValidation?: string;
  llmValidation?: string;
  walmartComment?: string;
  llmComment?: string;
  finalValidation?: string;
  finalComment?: string;
  rawData?: {
    walmart: string;
    llm: string;
    brand: string;
    competitors: Record<string, string>;
  };
}

interface TableRendererProps {
  // Data props
  tableRows: Record<string, string>[];
  selectedCells: Set<string>;
  finalValues: EnhancedFinalValue[];
  
  // State props
  walmartValidation: Record<number, 'yes' | 'no' | ''>;
  llmValidation: Record<number, 'yes' | 'no' | ''>;
  walmartComments: Record<number, 'curated-from-base' | 'vpd' | 'validated' | 'unable-to-curate' | 'value-not-provided' | 'not-acceptable-value' | ''>;
  llmComments: Record<number, 'value-found-from-llm' | 'llm-value-mismatch' | 'no-value-found' | ''>;
  editableWalmartValues: Record<number, string>;
  llmValues: Record<number, string>;
  originalWalmartValues: Record<number, string>;
  competitorUrls: Record<string, string>;
  llmSourceUrl: string;
  rawJson: string;
  loading: boolean;
  editingCell: number | null;
  
  // Handler functions
  handleCellToggle: (rowIdx: number, col: string) => void;
  handleRowToggle: (rowIdx: number, row: Record<string, string>) => void;
  setWalmartValidation: React.Dispatch<React.SetStateAction<Record<number, 'yes' | 'no' | ''>>>;
  setLlmValidation: React.Dispatch<React.SetStateAction<Record<number, 'yes' | 'no' | ''>>>;
  setWalmartComments: React.Dispatch<React.SetStateAction<Record<number, 'curated-from-base' | 'vpd' | 'validated' | 'unable-to-curate' | 'value-not-provided' | 'not-acceptable-value' | ''>>>;
  setLlmComments: React.Dispatch<React.SetStateAction<Record<number, 'value-found-from-llm' | 'llm-value-mismatch' | 'no-value-found' | ''>>>;
  setEditableWalmartValues: React.Dispatch<React.SetStateAction<Record<number, string>>>;
  setEditingCell: React.Dispatch<React.SetStateAction<number | null>>;
  saveEdit: (rowIdx: number) => void;
  cancelEdit: () => void;
  
  // Utility functions
  cleanNaNValue: (value: any) => string;
  isURL: (text: string) => boolean;
}

export default function TableRenderer({
  tableRows,
  selectedCells,
  finalValues,
  walmartValidation,
  llmValidation,
  walmartComments,
  llmComments,
  editableWalmartValues,
  llmValues,
  originalWalmartValues,
  competitorUrls,
  llmSourceUrl,
  rawJson,
  loading,
  editingCell,
  handleCellToggle,
  handleRowToggle,
  setWalmartValidation,
  setLlmValidation,
  setWalmartComments,
  setLlmComments,
  setEditableWalmartValues,
  setEditingCell,
  saveEdit,
  cancelEdit,
  cleanNaNValue,
  isURL
}: TableRendererProps) {

  // Validation status configurations
  const getValidationConfig = (status: string) => {
    switch (status) {
      case 'Yes':
        return { icon: <Check className="w-4 h-4" />, color: 'text-green-600', bgColor: 'bg-green-100', label: 'Yes' };
      case 'No':
        return { icon: <X className="w-4 h-4" />, color: 'text-red-600', bgColor: 'bg-red-100', label: 'No' };
      default:
        return { icon: null, color: 'text-gray-400', bgColor: 'bg-gray-50', label: 'Select Status' };
    }
  };

  const getTableHeaders = () => {
    if (tableRows.length === 0) return [];

    const baseHeaders = Object.keys(tableRows[0]);
    const orderedHeaders = [];

    orderedHeaders.push('Attribute');
    if (baseHeaders.includes('Walmart')) {
      orderedHeaders.push('Walmart', 'Validate Walmart', 'Prefilled Value Comments');
    }
    orderedHeaders.push('LLM', 'Validate LLM', 'LLM Comment');
    baseHeaders.forEach(header => {
      if (!['Attribute', 'Walmart'].includes(header)) {
        orderedHeaders.push(header);
      }
    });
    orderedHeaders.push('Final Validation', 'Final Comment', 'Final Value', 'Source');

    return orderedHeaders;
  };

  const renderCompetitorHeader = (col: string) => {
    const url = competitorUrls[col];
    if (url) {
      return (
        <button
          onClick={() => window.open(url, '_blank', 'noopener,noreferrer')}
          className="flex items-center justify-center gap-2 text-sm font-bold text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline transition-colors cursor-pointer group w-full"
          title={`Visit ${col} - ${url}`}
        >
          <span>{col}</span>
          <ExternalLink className="w-3 h-3 opacity-60 group-hover:opacity-100" />
        </button>
      );
    }
    return (
      <span className="text-sm font-bold break-words" title={col}>
        {col}
      </span>
    );
  };

  const renderLLMHeader = () => {
    if (llmSourceUrl) {
      return (
        <button
          onClick={() => window.open(llmSourceUrl, '_blank', 'noopener,noreferrer')}
          className="flex items-center justify-center gap-2 text-sm font-bold text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 hover:underline transition-colors cursor-pointer group w-full"
          title={`Visit LLM Source - ${llmSourceUrl}`}
        >
          <Sparkles className="w-4 h-4" />
          <span>LLM</span>
          <ExternalLink className="w-3 h-3 opacity-60 group-hover:opacity-100" />
        </button>
      );
    }

    return (
      <div className="flex items-center justify-center gap-1">
        <Sparkles className="w-4 h-4 text-purple-600" />
        <span className="text-sm font-bold">LLM</span>
      </div>
    );
  };
  
  // Removed unused comment configuration helpers to reduce bundle and satisfy TS

  const renderTableCell = (col: string, rowIdx: number, row: Record<string, string>) => {
    const key = `${rowIdx}-${col}`;
    const isSelected = selectedCells.has(key);

    // ✅ Updated getColumnWidth function - NO max-width constraints
    const getColumnWidth = (columnName: string) => {
      switch (columnName) {
        case 'Attribute':
          return 'w-48 min-w-[12rem]';
        case 'Walmart':
        case 'LLM':
          return 'w-64 min-w-[16rem]';
        case 'Validate Walmart':
        case 'Validate LLM':
          return 'w-44 min-w-[11rem] max-w-[11rem]';
        // ✅ ADD THESE CASES:
        case 'Prefilled Value Comments':
          return 'w-48 min-w-[12rem] max-w-[12rem]';
        case 'LLM Comment':
          return 'w-44 min-w-[11rem] max-w-[11rem]';
        case 'Final Validation':
          return 'w-32 min-w-[8rem]';
        case 'Final Comment':
          return 'w-52 min-w-[13rem]';
        case 'Final Value':
          return 'w-52 min-w-[13rem]';
        case 'Source':
          return 'w-32 min-w-[8rem]';
        default:
          return 'w-72 min-w-[18rem]';
      }
    };


    switch (col) {
      case 'Attribute':
        return (
          <td
            className={`border border-gray-300 dark:border-gray-600 px-3 py-2 font-semibold text-gray-900 dark:text-gray-100 cursor-pointer bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-900 dark:hover:to-blue-800 transition-all duration-200 sticky left-0 z-10 shadow-sm ${getColumnWidth(col)}`}
            onClick={() => handleRowToggle(rowIdx, row)}
            title={row[col]} // ✅ Full text on hover
          >
            <div
              className="block text-sm font-semibold leading-relaxed break-words"
              style={{
                wordBreak: 'break-word',
                overflowWrap: 'break-word',
                whiteSpace: 'normal',
                lineHeight: '1.4',
                hyphens: 'auto'
              }}
            >
              {/* ✅ Display FULL attribute name - NO truncation */}
              {row[col]}
            </div>
          </td>
        );

      case 'Walmart':
        const isEditing = editingCell === rowIdx;
        const originalValue = processDoublePipe(cleanNaNValue(originalWalmartValues[rowIdx]), tableRows[rowIdx]?.Attribute || '');
        const currentValue = processDoublePipe(cleanNaNValue(editableWalmartValues[rowIdx]) || "-", tableRows[rowIdx]?.Attribute || '');
        const hasEdited = currentValue !== "-" && currentValue !== originalValue;
        const isEmpty = currentValue === "-";

        return (
          <td
            className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer relative group hover:shadow-md transition-all duration-200 ${getColumnWidth(col)} 
              ${isSelected ? "ring-2 ring-blue-400 bg-blue-200 dark:bg-blue-800" : ""}
              ${hasEdited ? "bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/30 dark:to-yellow-800/30 ring-1 ring-yellow-300 dark:ring-yellow-600" : ""}
              ${!hasEdited && !isEmpty ? "bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30" : ""}
              ${isEmpty ? "bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/30 italic text-gray-400" : ""}`}
            onClick={() => {
              if (!loading && !isEditing) {
                handleCellToggle(rowIdx, col);
              }
            }}
          >
            <div className="flex items-start gap-2">
              <input
                type="checkbox"
                checked={isSelected}
                readOnly
                className="mt-1 h-4 w-4 rounded border border-blue-400 text-blue-600 focus:ring-2 focus:ring-blue-500 cursor-pointer flex-shrink-0"
                onClick={(e) => {
                  e.stopPropagation();
                  handleCellToggle(rowIdx, col);
                }}
              />

              <div className="relative flex-1 min-w-0">
                {hasEdited && !isEditing && (
                  <div className="absolute -top-2 -right-2 z-20">
                    <div className="flex items-center gap-1 px-2 py-0.5 rounded-full bg-orange-500 text-white text-xs font-bold shadow-lg">
                      <Edit2 size={10} />
                      <span>EDITED</span>
                    </div>
                  </div>
                )}

                <button
                  type="button"
                  title="Edit Walmart value"
                  onClick={(e) => {
                    e.stopPropagation();
                    setEditingCell(rowIdx);
                  }}
                  className={`absolute -top-1 -right-1 p-1 rounded-full bg-white dark:bg-gray-800 border 
                    ${hasEdited ? "border-orange-400 text-orange-600" : "border-blue-300 dark:border-blue-600 opacity-0 group-hover:opacity-100"
                    } hover:text-blue-600 hover:border-blue-500 hover:shadow-md transition-all duration-300 z-10`}
                >
                  <Edit2 size={12} className={hasEdited ? "text-orange-500" : "text-blue-500"} />
                </button>

                {!isEditing && (
                  <div
                    className={`block text-left font-medium text-sm leading-relaxed pr-6 break-words
                      ${hasEdited ? "text-orange-800 dark:text-orange-300 font-semibold" : ""}
                      ${isEmpty ? "italic text-gray-400 dark:text-gray-500" : ""}`}
                    style={{
                      wordBreak: 'break-word',
                      overflowWrap: 'break-word',
                      whiteSpace: 'normal',
                      lineHeight: '1.4'
                    }}
                    title={currentValue} // ✅ Full text on hover
                  >
                    {/* ✅ Display FULL value - NO truncation */}
                    {currentValue}
                  </div>
                )}

                {isEditing && (
                  <div className="space-y-2">
                    <textarea
                      value={currentValue === "-" ? "" : currentValue}
                      autoFocus
                      rows={4}
                      onChange={(e) => {
                        setEditableWalmartValues((prev) => ({
                          ...prev,
                          [rowIdx]: e.target.value,
                        }));
                      }}
                      onBlur={() => saveEdit(rowIdx)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !e.shiftKey) {
                          e.preventDefault();
                          saveEdit(rowIdx);
                        }
                        if (e.key === "Escape") {
                          cancelEdit();
                        }
                      }}
                      className="w-full border-2 border-orange-400 rounded px-2 py-1 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-orange-500 text-sm resize-none"
                      placeholder="Enter Walmart value..."
                    />
                    {originalValue !== "-" && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {/* ✅ Show FULL original value - NO truncation */}
                        <div className="font-medium">Original:</div>
                        <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs break-words">
                          {originalValue}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </td>
        );

      case 'Validate Walmart':
        const walmartConfig = getValidationConfig(walmartValidation[rowIdx] || '');
        return (
          <td className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30 ${getColumnWidth(col)}`}>
            <div className="flex flex-col items-center gap-2">
              {walmartValidation[rowIdx] && (
                <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${walmartConfig.bgColor} ${walmartConfig.color}`}>
                  {walmartConfig.icon}
                  <span className="text-xs font-semibold">
                    {walmartConfig.label}
                  </span>
                </div>
              )}
              <div className="relative">
                <select
                  value={walmartValidation[rowIdx] || ''}
                  onChange={(e) => setWalmartValidation(prev => ({
                    ...prev,
                    [rowIdx]: e.target.value as any
                  }))}
                  className="appearance-none bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer hover:border-blue-400 transition-colors"
                >
                  <option value="">Select...</option>
                  <option value="Yes">✅ Yes</option>
                  <option value="No">❌ No</option>
                </select>
                <ChevronDown className="absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </td>
        );

      case 'Prefilled Value Comments':
        return (
          <td className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30 w-48 min-w-[12rem] max-w-[12rem]`}>
            <div className="flex flex-col items-center gap-2">
              {walmartComments[rowIdx] && (
                <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                  <MessageSquare className="w-3 h-3" />
                  <span className="text-xs font-semibold">
                    {walmartComments[rowIdx].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                </div>
              )}
              <div className="relative">
                <select
                  value={walmartComments[rowIdx] || ''}
                  onChange={(e) => setWalmartComments(prev => ({
                    ...prev,
                    [rowIdx]: e.target.value as any
                  }))}
                  className="appearance-none bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer hover:border-blue-400 transition-colors"
                >
                  <option value="">Select...</option>
                  <option value="curated-from-base">Curated from base data</option>
                  <option value="curated-from-walmart-latest">Curated from Walmart Latest</option>
                  <option value="walmart-has-different-value"> Walmart has different value </option>                      
                  <option value="vpd">VPD</option>
                  <option value="validated">Validated</option>
                  <option value="unable-to-curate">Unable to curate</option>
                  <option value="value-not-provided">Value not provided</option>
                  <option value="not-acceptable-value">Not in acceptable value</option>
                </select>
                <ChevronDown className="absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </td>
        );

      case 'LLM':
        const llmKey = `${rowIdx}-LLM`;
        const isLlmSelected = selectedCells.has(llmKey);
        const llmValue = cleanNaNValue(llmValues[rowIdx]);

        // ✅ Get source URL directly from rawJson for this specific row
        let cellSourceUrl = "";
        try {
          const parsedData = JSON.parse(rawJson || '{}');
          const currentAttribute = tableRows[rowIdx]?.Attribute;
          const llmSourceData = parsedData.llm_suggested?.[currentAttribute];
          cellSourceUrl = llmSourceData?.source_url || "";
        } catch (e) {
          console.warn("Could not parse rawJson for LLM source URL");
        }

        // ✅ Check if cell has valid data (both value and source URL)
        const hasValidData = llmValue && llmValue !== "-" && cellSourceUrl && cellSourceUrl !== "-" && cellSourceUrl !== "null";

        return (
          <td
            className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/30 cursor-pointer hover:shadow-md transition-all duration-200 ${getColumnWidth(col)} ${isLlmSelected ? "ring-2 ring-purple-400 bg-purple-200 dark:bg-purple-800" : ""}`}
            onClick={() => handleCellToggle(rowIdx, 'LLM')}
            title={`${llmValue}${hasValidData ? ` - Click to visit source: ${cellSourceUrl}` : ''}`}
          >
            <div className="flex items-start gap-2">
              <input
                type="checkbox"
                checked={isLlmSelected}
                readOnly
                className="mt-1 h-4 w-4 rounded border border-purple-400 text-purple-600 focus:ring-2 focus:ring-purple-500 cursor-pointer flex-shrink-0"
                onClick={(e) => {
                  e.stopPropagation();
                  handleCellToggle(rowIdx, 'LLM');
                }}
              />
              <div className="flex-1 min-w-0">
                {hasValidData ? (
                  // ✅ Clickable version with source URL
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      const a = document.createElement("a");
                      a.href = cellSourceUrl;
                      a.target = "_blank";
                      a.rel = "noopener noreferrer";
                      a.click();
                    }}
                    className="block text-left font-medium text-sm leading-relaxed break-words text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 hover:underline transition-colors w-full group"
                    style={{
                      wordBreak: 'break-word',
                      overflowWrap: 'break-word',
                      whiteSpace: 'normal',
                      lineHeight: '1.4'
                    }}
                    title={`Click to visit source: ${cellSourceUrl}`}
                  >
                    <div className="flex items-center gap-1 justify-between">
                      <span className="flex-1">{llmValue}</span>
                      <ExternalLink className="w-3 h-3 opacity-60 group-hover:opacity-100 flex-shrink-0" />
                    </div>
                  </button>
                ) : (
                  // ✅ Non-clickable version (no source URL or empty data)
                  <div
                    className={`block text-left font-medium text-sm leading-relaxed break-words ${llmValue === "-" ? "italic text-gray-400 dark:text-gray-500" : ""
                      }`}
                    style={{
                      wordBreak: 'break-word',
                      overflowWrap: 'break-word',
                      whiteSpace: 'normal',
                      lineHeight: '1.4'
                    }}
                  >
                    {llmValue}
                  </div>
                )}
              </div>
            </div>
          </td>
        );

      case 'Validate LLM':
        const llmConfig = getValidationConfig(llmValidation[rowIdx] || '');
        return (
          <td className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/30 ${getColumnWidth(col)}`}>
            <div className="flex flex-col items-center gap-2">
              {llmValidation[rowIdx] && (
                <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${llmConfig.bgColor} ${llmConfig.color}`}>
                  {llmConfig.icon}
                  <span className="text-xs font-semibold">
                    {llmConfig.label}
                  </span>
                </div>
              )}
              <div className="relative">
                <select
                  value={llmValidation[rowIdx] || ''}
                  onChange={(e) => setLlmValidation(prev => ({
                    ...prev,
                    [rowIdx]: e.target.value as any
                  }))}
                  className="appearance-none bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 cursor-pointer hover:border-purple-400 transition-colors"
                >
                  <option value="">Select...</option>
                  <option value="Yes">✅ Yes</option>
                  <option value="No">❌ No</option>
                </select>
                <ChevronDown className="absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </td>
        );

      case 'LLM Comment':
        return (
          <td className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/30 w-44 min-w-[11rem] max-w-[11rem]`}>
            <div className="flex flex-col items-center gap-2">
              {llmComments[rowIdx] && (
                <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-purple-100 text-purple-800">
                  <MessageSquare className="w-3 h-3" />
                  <span className="text-xs font-semibold">
                    {llmComments[rowIdx].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                </div>
              )}
              <div className="relative">
                <select
                  value={llmComments[rowIdx] || ''}
                  onChange={(e) => setLlmComments(prev => ({
                    ...prev,
                    [rowIdx]: e.target.value as any
                  }))}
                  className="appearance-none bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 cursor-pointer hover:border-purple-400 transition-colors"
                >
                  <option value="">Select...</option>
                  <option value="value-found-from-llm">Value found from LLM</option>
                  <option value="llm-value-mismatch">LLM Value Mismatch</option>
                  <option value="no-value-found">No Value Found</option>
                </select>
                <ChevronDown className="absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </td>
        );


      case 'Final Value':
        return (
          <td className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-center align-middle bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/30 font-medium ${getColumnWidth(col)}`}>
            <div
              className="block text-sm break-words"
              style={{
                wordBreak: 'break-word',
                overflowWrap: 'break-word',
                whiteSpace: 'normal',
                lineHeight: '1.4'
              }}
              title={finalValues[rowIdx]?.finalValue || "-"} // ✅ Full text on hover
            >
              {/* ✅ Display FULL final value - NO truncation */}
              {finalValues[rowIdx]?.finalValue || "-"}
            </div>
          </td>
        );

      case 'Source':
        return (
          <td className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-center align-middle bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950/50 dark:to-amber-900/30 font-medium ${getColumnWidth(col)}`}>
            <div
              className="block text-sm break-words"
              style={{
                wordBreak: 'break-word',
                overflowWrap: 'break-word',
                whiteSpace: 'normal',
                lineHeight: '1.4'
              }}
              title={finalValues[rowIdx]?.source || "-"} // ✅ Full text on hover
            >
              {/* ✅ Display FULL source - NO truncation */}
              {finalValues[rowIdx]?.source || "-"}
            </div>
          </td>
        );

      case 'Final Validation':
        const walmartValidationVal = walmartValidation[rowIdx] || '';
        const llmValidationVal = llmValidation[rowIdx] || '';
        const finalValidationDisplay = [walmartValidationVal, llmValidationVal].filter(v => v !== '').join(', ') || '-';

        return (
          <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-center align-middle bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950/50 dark:to-emerald-900/30 font-medium w-32 min-w-[8rem]">
            <div
              className="block text-sm break-words"
              style={{
                wordBreak: 'break-word',
                overflowWrap: 'break-word',
                whiteSpace: 'normal',
                lineHeight: '1.4'
              }}
              title={finalValidationDisplay}
            >
              {finalValidationDisplay}
            </div>
          </td>
        );

      case 'Final Comment':
        const walmartCommentVal = walmartComments[rowIdx] || '';
        const llmCommentVal = llmComments[rowIdx] || '';

        // Format comment values to readable text
        const formatComment = (comment: string) => {
          if (!comment) return '';
          return comment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        };

        const formattedComments = [
          formatComment(walmartCommentVal),
          formatComment(llmCommentVal)
        ].filter(v => v !== '').join(' | ') || '-';

        return (
          <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-center align-middle bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-950/50 dark:to-teal-900/30 font-medium w-52 min-w-[13rem]">
            <div
              className="block text-sm break-words"
              style={{
                wordBreak: 'break-word',
                overflowWrap: 'break-word',
                whiteSpace: 'normal',
                lineHeight: '1.4'
              }}
              title={formattedComments}
            >
              {formattedComments}
            </div>
          </td>
        );


      default:
        // ✅ Competitor columns - Show FULL content
        const cellValue = cleanNaNValue(row[col]);

        return (
          <td
            className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 hover:shadow-md transition-all duration-200 ${getColumnWidth(col)} ${isSelected ? "ring-2 ring-blue-400 bg-blue-100 dark:bg-blue-900" : ""}`}
            onClick={() => handleCellToggle(rowIdx, col)}
            title={cellValue} // ✅ Full text on hover
          >
            <div className="flex items-start gap-2">
              <input
                type="checkbox"
                checked={isSelected}
                readOnly
                className="mt-1 h-4 w-4 rounded border border-gray-400 text-blue-600 focus:ring-2 focus:ring-blue-500 flex-shrink-0"
              />
              <div className="flex-1 min-w-0">
                {isURL(cellValue) && cellValue !== "-" ? (
                  <a
                    href={cellValue}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline text-sm break-words block"
                    onClick={(e) => e.stopPropagation()}
                    style={{
                      wordBreak: 'break-word',
                      overflowWrap: 'break-word',
                      whiteSpace: 'normal',
                      lineHeight: '1.4'
                    }}
                  >
                    {/* ✅ Display FULL URL - NO truncation */}
                    {cellValue}
                  </a>
                ) : (
                  <div
                    className="block text-left text-sm leading-relaxed font-medium break-words"
                    style={{
                      wordBreak: 'break-word',
                      overflowWrap: 'break-word',
                      whiteSpace: 'normal',
                      lineHeight: '1.4'
                    }}
                  >
                    {/* ✅ Display FULL content - NO truncation */}
                    {cellValue}
                  </div>
                )}
              </div>
            </div>
          </td>
        );
    }
  };
  return (
    <div className="overflow-x-auto rounded-xl border border-gray-300 dark:border-gray-700 shadow-lg bg-white dark:bg-gray-900 mb-6">
      <table className="text-sm table-fixed" style={{ minWidth: '1800px' }}>
        <thead className="bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 sticky top-0 z-10">
          <tr>
            {getTableHeaders().map((col) => {
              const colWidth = col === 'Attribute' ? 'w-48 min-w-[12rem]' :
                (col === 'Walmart' || col === 'LLM') ? 'w-64 min-w-[16rem]' :
                  (col.includes('Validate')) ? 'w-44 min-w-[11rem] max-w-[11rem]' :
                    col === 'Final Value' ? 'w-52 min-w-[13rem]' :
                      col === 'Source' ? 'w-32 min-w-[8rem]' :
                        'w-72 min-w-[18rem]';

              const isCompetitor = !['Attribute', 'Walmart', 'LLM', 'Final Validation', 'Final Comment', 'Final Value', 'Source'].includes(col) && !col.includes('Validate') && !col.includes('Comment');

              return (
                <th
                  key={col}
                  className={`border border-gray-400 dark:border-gray-500 px-3 py-3 font-bold text-gray-900 dark:text-gray-100 text-center shadow-sm ${colWidth} ${col === 'Attribute' ? 'sticky left-0 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 z-20' : ''
                    } ${col === 'Final Validation' ? 'bg-gradient-to-r from-emerald-100 to-emerald-200 dark:from-emerald-900/50 dark:to-emerald-800/50' : ''
                    } ${col === 'Final Comment' ? 'bg-gradient-to-r from-teal-100 to-teal-200 dark:from-teal-900/50 dark:to-teal-800/50' : ''
                    } ${isCompetitor ? 'bg-gradient-to-r from-orange-100 to-orange-200 dark:from-orange-900/50 dark:to-orange-800/50' : ''}`}
                >
                  {col === 'LLM' && renderLLMHeader()}
                  {col.startsWith('Validate') && (
                    <div className="flex items-center justify-center gap-1">
                      <CheckSquare className="w-4 h-4 text-green-600" />
                      <span className="text-xs font-bold">Validate</span>
                    </div>
                  )}
                  {!col.startsWith('Validate') && col !== 'LLM' && (
                    <>
                      {isCompetitor ? renderCompetitorHeader(col) : (
                        <span className="text-sm font-bold break-words" title={col}>
                          {col}
                        </span>
                      )}
                    </>
                  )}
                </th>
              );
            })}
          </tr>
        </thead>
        <tbody>
          {tableRows.map((row, rowIdx) => (
            <tr
              key={rowIdx}
              className="odd:bg-white even:bg-gray-50 hover:bg-blue-50 dark:odd:bg-gray-800 dark:even:bg-gray-850 dark:hover:bg-gray-700 transition-all duration-200 hover:shadow-sm"
            >
              {getTableHeaders().map((col, colIdx) => (
                <React.Fragment key={`${rowIdx}-${col}-${colIdx}`}>
                  {renderTableCell(col, rowIdx, row)}
                </React.Fragment>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
