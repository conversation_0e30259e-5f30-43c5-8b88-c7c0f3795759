import { useAppStore } from "../store/app";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";

export default function ModelSelect() {
  const model = useAppStore((s) => s.model);
  const setModel = useAppStore((s) => s.setModel);

  return (
    <Select value={model} onValueChange={setModel}>
      <SelectTrigger
        className="w-[220px] bg-white text-gray-800 border border-gray-300 rounded-md shadow-sm 
                   hover:border-blue-500 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
      >
        <SelectValue placeholder="Select model" />
      </SelectTrigger>
      <SelectContent
        position="popper"
        className="bg-white text-gray-800 shadow-lg border border-gray-200 rounded-md z-50"
      >
        <SelectItem value="gemini-1.5-flash">gemini-1.5-flash</SelectItem>
        <SelectItem value="gemini-1.5-pro">gemini-1.5-pro</SelectItem>
      </SelectContent>
    </Select>
  );
}
