import { Card, CardContent } from "@/components/ui/card";

export default function Profile() {
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    avatar:
      "https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=0D8ABC&color=fff",
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-6">
      <Card className="max-w-md w-full rounded-2xl shadow-lg border border-gray-200">
        <CardContent className="p-6 space-y-4 text-center">
          {/* Avatar */}
          <img
            src={user.avatar}
            alt={user.name}
            className="w-24 h-24 rounded-full mx-auto border-4 border-blue-500 shadow-md"
          />
          {/* Name */}
          <h2 className="text-2xl font-bold text-gray-800">{user.name}</h2>
          <p className="text-gray-600">{user.email}</p>
          <span className="inline-block px-3 py-1 bg-blue-100 text-blue-600 text-sm rounded-full">
            {user.role}
          </span>
        </CardContent>
      </Card>
    </div>
  );
}
