import { useState } from "react";
import { Switch } from "./ui/switch";
import { Check, X } from "lucide-react";

export default function GroundingToggle() {
  const [enabled, setEnabled] = useState(false);

  return (
    <div className="flex items-center space-x-2">
      <Switch
        id="grounding-toggle"
        checked={enabled}
        onCheckedChange={setEnabled}
        className="data-[state=checked]:bg-green-500"
      />
      <label
        htmlFor="grounding-toggle"
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        Enable Grounding
        {enabled ? (
          <Check className="w-4 h-4 text-green-500 ml-1 inline" />
        ) : (
          <X className="w-4 h-4 text-red-500 ml-1 inline" />
        )}
      </label>
    </div>
  );
}