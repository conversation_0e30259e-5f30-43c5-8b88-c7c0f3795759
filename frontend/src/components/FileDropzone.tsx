import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { useAppStore } from "../store/app";
import { api } from "../store/api";
import type { UploadResponse } from "../types";
import { useToast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";

export default function FileDropzone() {
  const { toast } = useToast();
  const setUploaded = useAppStore((s) => s.setUploaded);

  const [loading, setLoading] = useState(false);
  const [fileInfo, setFileInfo] = useState<{ name: string; size: number } | null>(null);

  const onDrop = useCallback(
    async (acceptedFiles: File[], fileRejections: import("react-dropzone").FileRejection[]) => {
      if (fileRejections.length > 0) {
        toast({
          title: "Invalid file type",
          description: "Only CSV or Excel files are allowed.",
          variant: "destructive",
        });
        return;
      }

      if (acceptedFiles.length === 0) return;
      const file = acceptedFiles[0];
      setFileInfo({ name: file.name, size: file.size });
      setLoading(true);

      const formData = new FormData();
      formData.append("file", file);

      try {
        // ✅ FIXED: call /assign/upload instead of /upload
        const res = await api.post<UploadResponse>("/upload", formData, {
          headers: { "Content-Type": "multipart/form-data" },
        });

        setUploaded(res.data);

        toast({
          title: "Upload successful!",
          description: `${res.data.preview.length} rows parsed.`,
        });
      } catch (err: any) {
        toast({
          title: `Upload failed`,
          description: err.message || "Check backend connection.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    },
    [setUploaded, toast]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    accept: {
      "text/csv": [".csv"],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx", ".xls"],
    },
  });

  return (
    <div
      {...getRootProps()}
      className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all ${
        isDragActive ? "border-blue-500 bg-blue-50" : "border-gray-300"
      }`}
    >
      <input {...getInputProps()} />

      {loading ? (
        <div className="flex flex-col items-center justify-center gap-2">
          <Loader2 className="animate-spin w-6 h-6 text-blue-500" />
          <p>Uploading...</p>
        </div>
      ) : fileInfo ? (
        <div className="text-gray-700">
          <p className="font-semibold">{fileInfo.name}</p>
          <p className="text-sm text-gray-500">{(fileInfo.size / 1024).toFixed(2)} KB</p>
          <p className="mt-2 text-gray-500">Drag another file to replace</p>
        </div>
      ) : isDragActive ? (
        <p className="text-blue-600">Drop the file here ...</p>
      ) : (
        <p>Drag & drop your CSV/Excel file here, or click to select</p>
      )}
    </div>
  );
}
