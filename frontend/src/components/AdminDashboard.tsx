import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { Loader2, Users, CheckCircle, Clock } from "lucide-react";
import { API_BASE_URL } from "../store/api";

type UserStatusSummary = {
  username: string;
  Pending: number;
  "In Progress": number;
  Completed: number;
};

export default function AdminDashboard() {
  const [userSummary, setUserSummary] = useState<UserStatusSummary[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchDashboard = async () => {
    setLoading(true);
    try {
      const res = await fetch(`${API_BASE_URL}/assign/users-status-summary`);
      if (!res.ok) throw new Error("Failed to fetch dashboard summary");
      const data: UserStatusSummary[] = await res.json();
      setUserSummary(data);
    } catch (err) {
      console.error("❌ Dashboard fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboard();
  }, []);

  // 👉 Aggregate totals
  const totals = userSummary.reduce(
    (acc, user) => {
      acc.Pending += user.Pending;
      acc["In Progress"] += user["In Progress"];
      acc.Completed += user.Completed;
      return acc;
    },
    { Pending: 0, "In Progress": 0, Completed: 0 }
  );

  return (
    <div className="p-8 min-h-screen bg-gradient-to-br from-indigo-50 via-white to-indigo-100 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 space-y-8">
      <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
        <Users className="w-7 h-7 text-[#FFC220]" />
        Admin Dashboard
      </h1>

      {loading ? (
        <div className="flex items-center justify-center h-40">
          <Loader2 className="w-8 h-8 animate-spin text-gray-600" />
        </div>
      ) : (
        <>
          {/* 🔹 Summary Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 ">
            {[
              { title: "Pending", value: totals.Pending, color: "text-gray-500", icon: Clock },
              { title: "In Progress", value: totals["In Progress"], color: "text-[#FFC220]", icon: Loader2 },
              { title: "Completed", value: totals.Completed, color: "text-green-600", icon: CheckCircle },
            ].map((stat) => {
              const Icon = stat.icon;
              return (
                <motion.div key={stat.title} whileHover={{ scale: 1.03 }}>
                  <Card className="shadow-md rounded-2xl border bg-white dark:bg-gray-900">
                    <CardContent className="p-6 flex flex-col items-center">
                      <Icon className={`${stat.color} w-8 h-8 mb-2 pt-2`} />
                      <p className="text-gray-600 dark:text-gray-400">{stat.title}</p>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        {stat.value}
                      </h2>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>

          {/* 🔹 User Table with Progress Bars */}
          <Card className="shadow-lg rounded-2xl border bg-white dark:bg-gray-900">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                User Progress
              </h2>
              <div className="overflow-x-auto">
                <table className="w-full text-left border-collapse">
                  <thead>
                    <tr className="text-gray-600 dark:text-gray-400 text-sm border-b">
                      <th className="py-2 px-4">User</th>
                      <th className="py-2 px-4">Pending</th>
                      <th className="py-2 px-4">In Progress</th>
                      <th className="py-2 px-4">Completed</th>
                    </tr>
                  </thead>
                  <tbody>
                    {userSummary.map((user) => {
                      return (
                        <tr key={user.username} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="py-2 px-4 font-medium text-gray-900 dark:text-white">
                            {user.username}
                          </td>
                          <td className="py-2 px-4 text-gray-600 dark:text-gray-400">{user.Pending}</td>
                          <td className="py-2 px-4 text-gray-600 dark:text-gray-400">{user["In Progress"]}</td>
                          <td className="py-2 px-4 text-gray-900 dark:text-white font-semibold">{user.Completed}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
