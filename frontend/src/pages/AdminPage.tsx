import FileDropzone from "../components/FileDropzone";
import { useAppStore } from "../store/app";
import { Button } from "../components/ui/button";
import { useEffect, useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";
import { API_BASE_URL } from "../store/api";
import AdminDashboard from "@/components/AdminDashboard";
import {
  Loader2,
  RotateCw,
  ChevronRight,
  Search,
  ChevronLeft,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { debounce } from "lodash";

interface Row {
  website_link: string;
  id: number;
  assign: string;
  website: string;
  category: string;
  product_name: string;
  product_type: string;
  item_id: string;
  status: string;
  task_url: string; // Add this line
}

export default function AdminPage() {
  const [rows, setRows] = useState<Row[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);
  const [search, setSearch] = useState("");
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [users, setUsers] = useState<{ username: string }[]>([]);
  const [loading, setLoading] = useState(false);
  const [statusMsg, setStatusMsg] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  const uploaded = useAppStore((s) => s.uploaded);
  const setUploaded = useAppStore((s) => s.setUploaded);
  const getFullDataForProcessing = useAppStore((s) => s.getFullDataForProcessing);
  const hasFullData = useAppStore((s) => s.hasFullData);
  const { toast } = useToast();

  // ------------------ Fetch Users ------------------
  useEffect(() => {
    async function fetchUsers() {
      try {
        const res = await fetch(`${API_BASE_URL}/users/list`);
        if (!res.ok) throw new Error("Failed to fetch users");
        const data = await res.json();
        setUsers(data.users);
      } catch (err: any) {
        toast({
          title: "Error loading users",
          description: err.message || "Failed to fetch users",
          variant: "destructive",
        });
      }
    }
    fetchUsers();
  }, [toast]);

  // ------------------ Assign Change ------------------
  const handleAssignChangeinPreview = (rowIdx: number, newUser: string) => {
    if (!uploaded || !uploaded.preview) return;
    const updatedPreview = [...uploaded.preview];
    updatedPreview[rowIdx] = { ...updatedPreview[rowIdx], assign: newUser };
    setUploaded({ ...uploaded, preview: updatedPreview });
  };

  // ------------------ Normalize & Allowed Fields ------------------
  function normalizeCol(name: string): string {
    return name
      .trim()
      .toLowerCase()
      .replace(/[^\w]+/g, "_")
      .replace(/_+/g, "_")
      .replace(/^_|_$/g, "");
  }

  const allowedFields = [
    "date_yyyy_mm_dd",
    "work_type",
    "associate_walmart_id",
    "item_status",
    "submission_id",
    "product_id_type",
    "product_id",
    "item_id",
    "audit_template_version",
    "initial_cq_score",
    "supplier_id",
    "item_created_date",
    "active_status",
    "website_link",
    "view_images",
    "category",
    "product_type_group",
    "product_type",
    "product_name",
    "brand",
    "main_image_url",
    "status",
    "assign",
  ];

  // ------------------ Save & Extract ------------------
  const handleSave = async () => {
    if (!uploaded) return;
    setLoading(true);
    setStatusMsg("⚡ Saving rows into BigQuery...");
    try {
      const rawData = getFullDataForProcessing();
      if (!rawData || rawData.length === 0) {
        throw new Error("No preview data available for storage");
      }
      const storageData = rawData.map((row) => {
        const normalized: Record<string, any> = {};
        for (const key in row) {
          const cleanKey = normalizeCol(key);
          if (allowedFields.includes(cleanKey)) {
            normalized[cleanKey] = row[key];
          }
        }
        return normalized;
      });

      const saveRes = await fetch(`${API_BASE_URL}/save`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ rows: storageData }),
      });
      if (!saveRes.ok) throw new Error("Failed to save rows to BigQuery");

      const runRes = await fetch(`${API_BASE_URL}/query/run`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          rows: storageData,
          bucket_name: "wal_test_bucket",
          blob_name: "original2/products.json",
        }),
      });
      if (!runRes.ok) throw new Error("Failed to run extraction");
      const runData = await runRes.json();

      setStatusMsg(
        `🎉 Extraction complete! Processed: ${runData.processed}, Successful: ${runData.successful || 0
        }, Failed: ${runData.failed || 0}`
      );
      toast({
        title: "Extraction Completed!",
        description: `Processed ${runData.successful || 0}/${runData.processed} rows`,
      });
    } catch (err: any) {
      setStatusMsg(`❌ Error: ${err.message}`);
      toast({
        title: "Extraction Failed",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAssignChange = async (rowIdx: number, newUser: string) => {
    // update rows state immediately so UI reflects
    const updatedRows = [...rows];
    updatedRows[rowIdx] = { ...updatedRows[rowIdx], assign: newUser };
    setRows(updatedRows);

    // also update uploaded if needed
    if (uploaded?.preview) {
      const updatedPreview = [...uploaded.preview];
      updatedPreview[rowIdx] = { ...updatedPreview[rowIdx], assign: newUser };
      setUploaded({ ...uploaded, preview: updatedPreview });
    }

    try {
      const itemId = updatedRows[rowIdx].item_id;
      const res = await fetch(`${API_BASE_URL}/tasks/assign`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username: newUser, item_id: itemId }),
      });

      if (!res.ok) throw new Error("Failed to assign task");
      const data = await res.json();

      toast({
        title: "✅ Task Assigned",
        description: data.message,
      });
    } catch (err: any) {
      toast({
        title: "❌ Assignment Failed",
        description: err.message,
        variant: "destructive",
      });
    }
  };


  // ------------------ Fetch Rows ------------------
  const fetchRows = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        limit: String(pageSize),
        offset: String((page - 1) * pageSize),
      });
      if (search) params.append("search", search);
      if (selectedUser) params.append("username", selectedUser);

      const res = await fetch(`${API_BASE_URL}/assign/?${params.toString()}`);
      const data = await res.json();

      setRows(data.rows || []);
      setTotalCount(data.count || 0);
    } catch (err) {
      console.error("Error fetching rows:", err);
    } finally {
      setLoading(false);
    }
  };

  // Debounced search
  const debouncedSetSearch = useMemo(() => debounce((val: string) => setSearch(val), 500), []);
  useEffect(() => {
    fetchRows();
  }, [page, search, selectedUser]);

  const totalPages = Math.ceil(totalCount / pageSize);

  // ------------------ Pagination Window ------------------
  const visiblePages = useMemo(() => {
    const windowSize = 5;
    let start = Math.max(1, page - Math.floor(windowSize / 2));
    let end = Math.min(totalPages, start + windowSize - 1);
    start = Math.max(1, end - windowSize + 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }, [page, totalPages]);

  // ------------------ Render ------------------
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-indigo-100 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 pt-20 p-8">
      <div className="max-w-6xl mx-auto space-y-10">
        {/* Header */}
        <motion.div
          className="text-center space-y-3"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h1 className="text-4xl font-extrabold text-gray-900 dark:text-white flex items-center justify-center gap-2">
            🛠{" "}
            <span className="bg-gradient-to-r from-indigo-600 to-pink-500 bg-clip-text text-transparent">
              Admin Task Manager
            </span>
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Upload your task list, assign users, and save directly to BigQuery.
          </p>
        </motion.div>

        {/* Data Info */}
        {uploaded && (
          <motion.div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4">
            <div className="flex gap-4 text-sm">
              <span className="font-medium text-blue-800 dark:text-blue-300">
                📊 Data Status:
              </span>
              <span className="text-blue-600 dark:text-blue-400">
                Preview: {uploaded.preview?.length || 0} rows (
                {uploaded.columns?.length || 0} cols)
              </span>
              {hasFullData() ? (
                <span className="text-green-600 dark:text-green-400">
                  ✅ Full Data: {uploaded.full_data?.length || 0} rows
                </span>
              ) : (
                <span className="text-amber-600 dark:text-amber-400">
                  ⚠️ Using preview data
                </span>
              )}
            </div>
          </motion.div>
        )}

        {/* File Drop */}
        <motion.div className="rounded-2xl bg-white/70 dark:bg-gray-800/70 p-6 shadow-xl border">
          <FileDropzone />
        </motion.div>

        {/* Preview Table */}
        {uploaded?.preview && uploaded?.columns ? (
          <motion.div className="rounded-2xl bg-white/80 dark:bg-gray-800/80 p-6 shadow-xl border">
            <h2 className="text-xl font-bold mb-5">📋 Preview Data</h2>
            <div className="overflow-x-auto max-h-[400px]">
              <table className="min-w-full text-sm">
                <thead className="sticky top-0 bg-gray-100 dark:bg-gray-700">
                  <tr>
                    {uploaded.columns.map((col) => (
                      <th key={col} className="px-4 py-2 border">
                        {col}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {uploaded.preview.map((row, idx) => (
                    <tr
                      key={idx}
                      className="even:bg-gray-50 dark:even:bg-gray-800"
                    >
                      {uploaded.columns!.map((col) => (
                        <td key={col} className="px-4 py-2 border">
                          {col === "assign" ? (
                            <Select
                              value={row.assign || ""}
                              onValueChange={(val) =>
                                handleAssignChangeinPreview(idx, val)
                              }
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Assign User" />
                              </SelectTrigger>
                              <SelectContent>
                                {users.map((u) => (
                                  <SelectItem
                                    key={u.username}
                                    value={u.username}
                                  >
                                    {u.username}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            row[col]
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Save */}
            <div className="flex justify-end mt-4">
              <Button onClick={handleSave} disabled={loading}>
                {loading ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  "💾 Save & Extract"
                )}
              </Button>
            </div>

            {/* Status */}
            <AnimatePresence>
              {statusMsg && (
                <motion.div
                  key={statusMsg}
                  className={`mt-4 text-sm ${statusMsg.startsWith("❌") ? "text-red-600" : "text-green-600"
                    }`}
                >
                  {statusMsg}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ) : (
          <motion.div className="text-center py-12 text-gray-500 italic">
            📂 Upload a file to preview and save data.
          </motion.div>
        )}
      </div>

      {/* Dashboard Section */}
      <motion.div className="max-w-6xl mx-auto mt-16">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Dashboard
          </h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setRefreshKey((prev) => prev + 1)}
          >
            <RotateCw className="h-4 w-4" /> Refresh
          </Button>
        </div>
        <AdminDashboard key={refreshKey} />

        {/* Filters */}
        <div className="mt-8 flex items-center gap-4 bg-gray-50 dark:bg-gray-900 p-4 rounded-xl shadow-sm">
          {/* Search Input */}
          <div className="relative flex-1 min-w-[250px]">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by product, category..."
              onChange={(e) => {
                setPage(1);
                debouncedSetSearch(e.target.value);
              }}
              className="pl-9 rounded-lg border-gray-300 dark:border-gray-600"
            />
          </div>

          {/* User Filter */}
          <Select
            value={selectedUser || "all"}
            onValueChange={(val) => {
              setPage(1);
              setSelectedUser(val === "all" ? "" : val);
            }}
          >
            <SelectTrigger className="w-[220px] rounded-lg border-gray-300 dark:border-gray-600">
              <SelectValue placeholder="Filter by User" />
            </SelectTrigger>
            <SelectContent className="bg-white dark:bg-gray-800 shadow-lg border rounded-lg">
              <SelectItem value="all">All Users</SelectItem>
              {users.map((u) => (
                <SelectItem
                  key={u.username}
                  value={u.username}
                  className="hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  {u.username}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>


        {/* Table */}
        <div className="mt-6 overflow-x-auto rounded-xl border border-gray-200 dark:border-gray-700 shadow bg-white dark:bg-gray-800">
          <table className="w-full border-collapse text-sm">
            <thead className="sticky top-0 z-10 bg-gray-100 dark:bg-gray-700 shadow-sm">
              <tr>
                {[
                  "S.No.",
                  "User",
                  "Website",
                  "Category",
                  "Product Name",
                  "Product Type",
                  "Item Id",
                  "Task URL", // Add this line
                  "Status",
                ].map((head) => (
                  <th
                    key={head}
                    className="border px-4 py-3 text-left font-semibold text-gray-700 dark:text-gray-200"
                  >
                    {head}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={9} className="text-center py-8">
                    <Loader2 className="w-6 h-6 animate-spin mx-auto text-gray-500" />
                  </td>
                </tr>
              ) : rows.length > 0 ? (
                rows.map((row, idx) => (
                  <tr
                    key={row.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors even:bg-gray-50/50 dark:even:bg-gray-800/50"
                  >
                    <td className="border px-4 py-3">
                      {(page - 1) * pageSize + idx + 1}
                    </td>

                    {/* User Dropdown */}
                    <td className="border px-4 py-3">
                      <Select
                        value={row.assign || ""}
                        onValueChange={(value) => handleAssignChange(idx, value)}
                      >
                        <SelectTrigger
                          className="w-[180px] rounded-lg"
                          disabled={row.status?.toLowerCase() === "completed"}
                        >
                          <SelectValue placeholder="Assign user" />
                        </SelectTrigger>
                        <SelectContent className="bg-white dark:bg-gray-800 shadow-lg border rounded-lg">
                          {users.map((u) => (
                            <SelectItem
                              key={u.username}
                              value={u.username}
                              className="hover:bg-gray-100 dark:hover:bg-gray-700"
                            >
                              {u.username}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </td>

                    <td className="border px-4 py-3">
                      {row.website_link ? (
                        <a
                          href={row.website_link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-[#0071CE] hover:underline"
                        >
                          Visit
                        </a>
                      ) : (
                        "-"
                      )}
                    </td>
                    <td className="border px-4 py-3">{row.category}</td>
                    <td className="border px-4 py-3">{row.product_name}</td>
                    <td className="border px-4 py-3">{row.product_type}</td>
                    <td className="border px-4 py-3">{row.item_id}</td>

                    {/* Add Task URL column */}
                    <td className="border px-4 py-3">
                      {row.task_url ? (
                        <a
                          href={row.task_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-[#0071CE] hover:underline"
                        >
                          Open Task
                        </a>
                      ) : (
                        "-"
                      )}
                    </td>

                    <td className="border px-4 py-3">{row.status}</td>
                  </tr>

                ))
              ) : (
                <tr>
                  <td colSpan={9} className="text-center py-8 text-gray-500">
                    No rows found
                  </td>
                </tr>
              )}
            </tbody>
          </table>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-4 px-4 py-2 border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="outline"
              size="sm"
              disabled={page === 1}
              onClick={() => setPage((p) => p - 1)}
              className="flex items-center gap-1"
            >
              <ChevronLeft className="h-4 w-4" /> Previous
            </Button>

            <div className="flex gap-2">
              {visiblePages.map((p) => (
                <Button
                  key={p}
                  variant={p === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setPage(p)}
                >
                  {p}
                </Button>
              ))}
            </div>

            <Button
              variant="outline"
              size="sm"
              disabled={page === totalPages}
              onClick={() => setPage((p) => p + 1)}
              className="flex items-center gap-1"
            >
              Next <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

      </motion.div>
    </div>
  );
}
