import { useState } from "react";
import { useAppStore } from "../store/app";
import { useNavigate, Link } from "react-router-dom";
import { Button } from "../components/ui/button";
import { User, Shield, Lock, Loader2, Eye, EyeOff } from "lucide-react"; // 👁️ Added Eye, EyeOff

export default function LoginPage() {
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false); // 👁️ State to toggle password
  const setRoleStore = useAppStore((s) => s.setRole);
  const navigate = useNavigate();

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && username && password) {
      handleLogin();
    }
  };

  const handleLogin = async () => {
    if (!username || !password) return;
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();
      if (!response.ok) {
        alert(data.detail || "Login failed");
        setLoading(false);
        return;
      }

      // ✅ Save to store & localStorage
      const userData = {
        username: data.username,
        email: data.email || "Not provided",
        role: data.role,
      };
      localStorage.setItem("user", JSON.stringify(userData));

      setRoleStore(userData.role, userData.username);
      navigate(userData.role === "admin" ? "/admin" : "/user");
    } catch (err) {
      console.error(err);
      alert("Login failed, check console");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-sm p-8 transform transition-all hover:scale-[1.02]">
        <h1 className="text-3xl font-extrabold text-center mb-6 text-indigo-700">
          Log In
        </h1>
        <p className="text-gray-500 text-center mb-8 text-sm">
          Sign in to continue to your workspace
        </p>

        <form onSubmit={(e) => { e.preventDefault(); handleLogin(); }}>
          {/* Username Input */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Username
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="Enter your username"
                className="w-full px-3 py-2 pl-10 border rounded-lg focus:ring-2 focus:ring-indigo-400 focus:outline-none"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={loading}
              />
              <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
          </div>

          {/* Password Input with 👁️ toggle */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                placeholder="Enter your password"
                className="w-full px-3 py-2 pl-10 pr-10 border rounded-lg focus:ring-2 focus:ring-indigo-400 focus:outline-none"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={loading}
              />
              <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />

              {/* 👁️ Toggle button */}
              <button
                type="button"
                className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                onClick={() => setShowPassword(!showPassword)}
                tabIndex={-1} // don’t steal focus while typing
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>

          {/* Login Button */}
          <Button
            type="submit"
            className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition flex items-center justify-center"
            disabled={!username || !password || loading}
          >
            {loading ? (
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
            ) : (
              <Shield className="w-5 h-5 mr-2" />
            )}
            {loading ? "Logging in..." : "Login"}
          </Button>
        </form>

        <p className="text-center text-gray-400 text-xs mt-6">
          Don’t have an account?{" "}
          <Link to="/signup" className="text-indigo-600 hover:underline">
            Sign Up
          </Link>
        </p>
        <p className="text-center text-gray-400 text-xs mt-6">
          © {new Date().getFullYear()} Task Manager. All rights reserved.
        </p>
      </div>
    </div>
  );
}
