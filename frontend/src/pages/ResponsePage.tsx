import { useLocation, useNavigate } from "react-router-dom";
import ResponseArea from "@/components/ResponseArea";
import { Button } from "../components/ui/button";
import { useEffect } from "react";

export default function ResponsePage() {
  const navigate = useNavigate();
  const location = useLocation();
  const selectedRow = location.state?.selectedRow || null;

  // ✅ Log data on mount
  useEffect(() => {
    console.log("📦 Received row from User Page:", selectedRow);
  }, [selectedRow]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-yellow-50 dark:from-gray-900 dark:via-gray-950 dark:to-gray-800 transition-colors">
      {/* Add top padding to account for fixed navbar */}
      <div className="pt-16"> {/* This pushes content below the navbar */}
        
        {/* Page Header - Now NOT fixed */}
        <div className="bg-gradient-to-r from-blue-50/95 via-white/95 to-yellow-50/95 dark:from-gray-900/95 dark:via-gray-950/95 dark:to-gray-800/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
          <div className="container mx-auto px-4 py-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
              <h1 className="text-2xl font-extrabold text-blue-800 dark:text-blue-400 flex items-center gap-2">
                🔍 Walmart Crawl Engine
              </h1>

              <Button
                variant="outline"
                onClick={() => navigate("/user")}
                className="rounded-xl border-blue-500 text-blue-700 hover:bg-blue-600 hover:text-white dark:border-blue-400 dark:text-blue-300 dark:hover:bg-blue-500 dark:hover:text-white px-4 py-2 text-sm sm:text-base transition-all duration-200 shadow-md hover:shadow-lg"
              >
                ← Back to Dashboard
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="container mx-auto px-4 py-6">
          <div className="w-full max-w-full">
            {selectedRow ? (
              <ResponseArea preloadedRow={selectedRow} />
            ) : (
              <div className="rounded-2xl bg-white dark:bg-gray-900 p-8 shadow-lg border border-yellow-300 dark:border-gray-700 text-center">
                <div className="max-w-md mx-auto">
                  <div className="mb-4">
                    <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-2xl">📊</span>
                    </div>
                  </div>
                  <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                    No Product Selected
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    Please select a product from the user dashboard to begin analysis.
                  </p>
                  <Button
                    onClick={() => navigate("/user")}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl shadow-md hover:shadow-lg transition-all duration-200"
                  >
                    Go to Dashboard
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
