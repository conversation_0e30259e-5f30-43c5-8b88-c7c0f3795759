import { useState } from "react";
import { useN<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Button } from "../components/ui/button";
import { User, Mail, Shield, Lock, Loader2 } from "lucide-react";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export default function SignUpPage() {
  const [role, setRole] = useState<"admin" | "user" | "">("");
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false); // NEW: loading state
  const navigate = useNavigate();

  const handleSignUp = async () => {
    if (!username || !email || !role || !password) return;
    if (password !== confirmPassword) {
      alert("Passwords do not match!");
      return;
    }

    setLoading(true); // Start loading
    try {
      const response = await fetch(`${API_BASE_URL}/signup`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username, email, password, role }),
      });

      const data = await response.json();

      if (!response.ok) {
        alert(data.detail || "Signup failed");
        setLoading(false); // Stop loading on error
        return;
      }

      alert(data.message);
      navigate("/login");
    } catch (err) {
      console.error(err);
      alert("Signup failed, check console");
      setLoading(false); // Stop loading on error
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-teal-100 px-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-sm p-8">
        <h1 className="text-3xl font-extrabold text-center mb-6 text-teal-700">
          Sign Up
        </h1>
        <p className="text-gray-500 text-center mb-8 text-sm">
          Create your account to get started
        </p>

        {/* Role Select */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Select Role
          </label>
          <select
            value={role}
            onChange={(e) => setRole(e.target.value as any)}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-400 focus:outline-none"
            disabled={loading} // disable while loading
          >
            <option value="">Choose role...</option>
            <option value="admin">👑 Admin</option>
            <option value="user">🙋 User</option>
          </select>
        </div>

        {/* Username */}
        <div className="mb-4 relative">
          <input
            type="text"
            placeholder="Enter username"
            className="w-full px-3 py-2 pl-10 border rounded-lg focus:ring-2 focus:ring-teal-400 focus:outline-none"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            disabled={loading} // disable while loading
          />
          <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
        </div>

        {/* Email */}
        <div className="mb-4 relative">
          <input
            type="email"
            placeholder="Enter email"
            className="w-full px-3 py-2 pl-10 border rounded-lg focus:ring-2 focus:ring-teal-400 focus:outline-none"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={loading}
          />
          <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
        </div>

        {/* Password */}
        <div className="mb-4 relative">
          <input
            type="password"
            placeholder="Enter password"
            className="w-full px-3 py-2 pl-10 border rounded-lg focus:ring-2 focus:ring-teal-400 focus:outline-none"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={loading}
          />
          <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
        </div>

        {/* Confirm Password */}
        <div className="mb-6 relative">
          <input
            type="password"
            placeholder="Confirm password"
            className="w-full px-3 py-2 pl-10 border rounded-lg focus:ring-2 focus:ring-teal-400 focus:outline-none"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            disabled={loading}
          />
          <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
        </div>

        {/* Sign Up */}
        <Button
          className="w-full bg-teal-600 hover:bg-teal-700 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center"
          onClick={handleSignUp}
          disabled={!role || !username || !email || !password || !confirmPassword || loading} // disable while loading
        >
          {loading ? (
            <Loader2 className="w-5 h-5 mr-2 animate-spin" />
          ) : (
            <Shield className="w-5 h-5 mr-2" />
          )}
          {loading ? "Signing up..." : "Sign Up"}
        </Button>

        <p className="text-center text-gray-400 text-xs mt-6">
          Already have an account?{" "}
          <Link to="/login" className="text-teal-600 hover:underline">
            Log In
          </Link>
        </p>
      </div>
    </div>
  );
}
