import { useState, useEffect, useRef } from "react";
import { Button } from "../components/ui/button";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import {
  Check<PERSON>ir<PERSON>,
  Clock,
  Loader,
  BarChart3,
  FilePlus2,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

type Status = "Pending" | "In Progress" | "Completed";

type WalmartRow = {
  id_str: string;
  submission_id: string;
  url: string;
  domain: string;
  product_type: string;
  product_name?: string;
  status?: Status;
  website_link?: string;
  category?: string;
  item_id?: string;
  [key: string]: any;
};

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export default function UserPage() {
  const { toast } = useToast();

  const [rows, setRows] = useState<WalmartRow[]>([]);
  const [currentTask, setCurrentTask] = useState<WalmartRow | null>(null);
  const [search, setSearch] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [researchingId, setResearchingId] = useState<string | null>(null);
  const [highlightedId, setHighlightedId] = useState<string | null>(null);
  const [statusCounts, setStatusCounts] = useState<{
    Pending: number;
    "In Progress": number;
    Completed: number;
  }>({ Pending: 0, "In Progress": 0, Completed: 0 });

  const navigate = useNavigate();
  const currentTaskRef = useRef<HTMLDivElement | null>(null);

  // Get current logged-in user info from localStorage
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const username = user?.username || "";

  // Fetch rows for table
  const fetchRows = async () => {
    if (!username) return;
    try {
      setLoading(true);
      const res = await fetch(
        `${API_BASE_URL}/assign/?limit=${pageSize}&offset=${(page - 1) * pageSize
        }&search=${encodeURIComponent(search)}&username=${username}`
      );
      const data = await res.json();
      setRows(data.rows || []);
      setTotalCount(data.count || 0);
    } catch (err) {
      console.error("Failed to fetch rows:", err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch status summary
  const fetchStatusCounts = async () => {
    if (!username) return;
    try {
      const res = await fetch(
        `${API_BASE_URL}/assign/status-summary?username=${username}`
      );
      const data = await res.json();
      setStatusCounts({
        Pending: data.Pending || 0,
        "In Progress": data["In Progress"] || 0,
        Completed: data.Completed || 0,
      });
    } catch (err) {
      console.error("Failed to fetch status summary:", err);
    }
  };

  // Claim next task handler
  const handleClaimTask = async () => {
    if (!username) return;
    try {
      setLoading(true);

      const res = await fetch(`${API_BASE_URL}/claim/button`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          user_id: username
        })
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.detail || "Failed to claim task");
      }

      if (data.status === "success") {
        const task = {
          id_str: data.bigquery_row?.id_str || "",
          submission_id: data.bigquery_row?.submission_id || "",
          url: data.url,
          domain: data.bigquery_row?.domain || "",
          product_type: data.bigquery_row?.product_type || "",
          product_name: data.bigquery_row?.product_name || "",
          status: "In Progress",
          website_link: data.url,
          category: data.bigquery_row?.category || "",
          item_id: data.bigquery_row?.item_id || "",
          ...data.bigquery_row
        };

        setCurrentTask(task);
        setHighlightedId(task.id_str);

        setTimeout(() => {
          currentTaskRef.current?.scrollIntoView({ behavior: "smooth" });
          setHighlightedId(null);
        }, 400);

        fetchRows();
        fetchStatusCounts();

        toast({
          title: "✅ Task claimed successfully!",
          description: `URL: ${data.url}`,
          duration: 4000,
        });

      } else if (data.status === "empty") {
        toast({ title: "⚠️ No tasks available", description: data.message });
      } else if (data.status === "failed") {
        toast({ title: "❌ Failed to claim task", description: `URL: ${data.url}` });
      } else {
        toast({ title: "Unexpected response", description: "Please try again." });
      }
    } catch (err) {
      toast({
        title: "❌ Error",
        description: err instanceof Error ? err.message : "Unknown error",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResearch = async (row: WalmartRow) => {
    if (!username) return;
    try {
      setResearchingId(row.id_str);

      console.group("🔍 UserPage handleResearch Debug");
      console.log("Original row:", row);
      console.log("Row keys:", Object.keys(row));
      console.log("website_link:", row.website_link);
      console.log("url:", row.url);
      console.groupEnd();

      const res = await fetch(`${API_BASE_URL}/assign/update-status`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id_str: row.id_str,
          new_status: "In Progress",
          username,
        }),
      });

      if (!res.ok) throw new Error("Failed to update status");
      const updatedRow: WalmartRow = await res.json();

      console.group("🔍 Backend Response Debug");
      console.log("updatedRow from backend:", updatedRow);
      console.log("updatedRow keys:", Object.keys(updatedRow));
      console.log("Backend website_link:", updatedRow.website_link);
      console.log("Backend url:", updatedRow.url);
      console.groupEnd();

      const transformedRow = {
        ...updatedRow,
        website_link: updatedRow.website_link ||
          updatedRow.url ||
          row.website_link ||
          row.url ||
          "",
        category: updatedRow.category || row.category || "",
        product_name: updatedRow.product_name || row.product_name || "",
        product_type: updatedRow.product_type || row.product_type || "",
        item_id: updatedRow.item_id || row.item_id || "",
        submission_id: updatedRow.submission_id || row.submission_id || "",
        product_id: updatedRow.product_id || row.product_id || "", // New field
      };

      console.group("✅ Final Transformed Data");
      console.log("transformedRow:", transformedRow);
      console.log("Final website_link:", transformedRow.website_link);
      console.log("transformedRow keys:", Object.keys(transformedRow));
      console.groupEnd();

      setRows((prev) =>
        prev.map((r) => (r.id_str === updatedRow.id_str ? transformedRow : r))
      );

      await fetchStatusCounts();

      navigate("/response", { state: { selectedRow: transformedRow } });

    } catch (err) {
      console.error("Failed to update status:", err);
      alert("Failed to update status.");
    } finally {
      setResearchingId(null);
    }
  };

  // Fetch data automatically when dependencies change
  useEffect(() => {
    fetchRows();
    fetchStatusCounts();
  }, [page, pageSize, username, search]);

  const totalPages = Math.ceil(totalCount / pageSize);

  // Status icon & color mapping - Enhanced for dark mode
  const statusConfig: Record<Status, { color: string; icon: any }> = {
    Pending: { color: "text-gray-500 dark:text-gray-400", icon: Clock },
    "In Progress": { color: "text-amber-600 dark:text-amber-400", icon: Loader },
    Completed: { color: "text-green-600 dark:text-green-400", icon: CheckCircle },
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 p-6 space-y-8 transition-colors">
      {/* Header & dashboard cards */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-[#0071CE] dark:text-blue-400 flex items-center gap-2">
          <BarChart3 className="w-6 h-6 text-[#FFC220] dark:text-yellow-400" />
          User Dashboard
        </h1>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
        {Object.entries(statusCounts).map(([status, count]) => {
          const Icon = statusConfig[status as Status]?.icon;
          return (
            <motion.div
              key={status}
              whileHover={{ scale: 1.05 }}
              className="shadow-lg rounded-2xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 hover:shadow-xl transition-all duration-300"
            >
              <Card>
                <CardContent className="m-2 p-6 flex flex-col items-center justify-center space-y-3">
                  {Icon && (
                    <Icon
                      className={`${statusConfig[status as Status].color} w-8 h-8`}
                    />
                  )}
                  <p className="text-gray-600 dark:text-gray-300 font-medium">
                    {status}
                  </p>
                  <h2 className="text-3xl font-bold text-[#0071CE] dark:text-blue-400">{count}</h2>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Current Task Section - Enhanced dark mode */}
      <motion.div
        ref={currentTaskRef}
        initial={{ opacity: 0, y: 20 }}
        animate={{
          opacity: 1,
          y: 0,
          backgroundColor:
            highlightedId && currentTask?.id_str === highlightedId
              ? "#FFF7CC"
              : "transparent",
        }}
        transition={{ duration: 0.6 }}
        className="bg-white dark:bg-gray-900 p-8 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700"
      >
        {!currentTask ? (
          <>
            <h2 className="text-xl font-bold text-[#0071CE] dark:text-blue-400 mb-4 text-center">
              🎯 No Current Task
            </h2>
            <p className="text-center text-gray-500 dark:text-gray-400 mb-6">
              Click below to start working on your next available task.
            </p>
            <div className="flex justify-center">
              <Button
                size="lg"
                onClick={handleClaimTask}
                className="bg-[#0071CE] hover:bg-[#005fa3] dark:bg-blue-600 dark:hover:bg-blue-700 text-white font-semibold rounded-full px-6 py-3 flex items-center gap-2 transition-colors"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader className="animate-spin h-5 w-5" />
                    Claiming...
                  </>
                ) : (
                  <>
                    <FilePlus2 className="w-5 h-5" />
                    Claim Next Task
                  </>
                )}
              </Button>
            </div>
          </>
        ) : (
          <>
            {/* Header with status */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-[#0071CE] dark:text-blue-400 flex items-center gap-2">
                Current Task
              </h2>
              <span
                className={`px-4 py-1 rounded-full text-sm font-medium ${currentTask.status === "Completed"
                  ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                  : currentTask.status === "In Progress"
                    ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300"
                    : "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
                  }`}
              >
                {currentTask.status || "Pending"}
              </span>
            </div>

            {/* Task details grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8">
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Website</p>
                {currentTask.website_link ? (
                  <a
                    href={currentTask.website_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#0071CE] dark:text-blue-400 hover:underline font-medium transition-colors"
                  >
                    {currentTask.domain || "Visit Website"}
                  </a>
                ) : (
                  <p className="text-gray-600 dark:text-gray-300">-</p>
                )}
              </div>
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Category</p>
                <p className="font-medium text-gray-700 dark:text-gray-200">
                  {currentTask.category || "-"}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Product Name</p>
                <p className="font-medium text-gray-700 dark:text-gray-200">
                  {currentTask.product_name || "Unnamed Product"}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Product Type</p>
                <p className="font-medium text-gray-700 dark:text-gray-200">
                  {currentTask.product_type || "-"}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Item ID</p>
                <p className="font-medium text-gray-700 dark:text-gray-200">
                  {currentTask.item_id || "-"}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Submission ID</p>
                <p className="font-medium text-gray-700 dark:text-gray-200">
                  {currentTask.submission_id || "-"}
                </p>
              </div>
            </div>

            {/* Start Research Button */}
            <div className="flex justify-end gap-3">
              <Button
                size="sm"
                onClick={() => handleResearch(currentTask)}
                className="bg-[#FFC220] hover:bg-[#e6ad00] dark:bg-yellow-500 dark:hover:bg-yellow-600 text-black dark:text-gray-900 font-semibold rounded-full px-5 transition-colors"
                disabled={researchingId === currentTask.id_str}
              >
                {researchingId === currentTask.id_str ? (
                  <>
                    <Loader className="animate-spin mr-2 h-4 w-4" />
                    Loading...
                  </>
                ) : (
                  "Start Research"
                )}
              </Button>
            </div>
          </>
        )}
      </motion.div>

      {/* Search Input & Button - Enhanced dark mode */}
      <div className="flex items-center gap-3">
        <input
          type="text"
          placeholder="🔍 Search..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") setPage(1);
          }}
          className="border border-gray-300 dark:border-gray-600 px-3 py-2 rounded-lg shadow-sm focus:ring-2 focus:ring-[#0071CE] dark:focus:ring-blue-500 focus:outline-none w-72 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 transition-colors"
        />
        <Button
          size="sm"
          className="bg-[#FFC220] hover:bg-[#e6ad00] dark:bg-yellow-500 dark:hover:bg-yellow-600 text-black dark:text-gray-900 font-semibold transition-colors"
          onClick={() => setPage(1)}
        >
          Search
        </Button>
      </div>

      {/* Previous Tasks Table - Enhanced dark mode */}
      <div className="overflow-x-auto shadow-lg rounded-xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-bold text-[#0071CE] dark:text-blue-400 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          Previous Tasks
        </h2>
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-[#0071CE] dark:bg-gray-800">
            <tr>
              {[
                "S.No.",
                "Website",
                "Category",
                "Product Name",
                "Product Type",
                "Item Id",
                "GTIN", // ✅ New column
                "Status",
                "Action",
              ].map((header) => (
                <th
                  key={header}
                  className="px-4 py-3 text-left text-sm font-semibold text-white dark:text-gray-200"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 dark:divide-gray-800">
            {loading ? (
              <tr>
                <td
                  colSpan={9} // ✅ updated colSpan since we added 1 new column
                  className="text-center py-6 text-gray-500 dark:text-gray-400"
                >
                  <Loader className="animate-spin h-6 w-6 mx-auto mb-2" />
                  Loading...
                </td>
              </tr>
            ) : rows.length === 0 ? (
              <tr>
                <td
                  colSpan={9} // ✅ updated colSpan
                  className="text-center py-6 text-gray-500 dark:text-gray-400"
                >
                  No data found
                </td>
              </tr>
            ) : (
              rows.map((row, idx) => {
                const serial = (page - 1) * pageSize + idx + 1;

                return (
                  <motion.tr
                    key={`${row.id_str}-${serial}`}
                    initial={highlightedId === row.id_str ? { backgroundColor: "#FFF7CC" } : {}}
                    animate={{ backgroundColor: "transparent" }}
                    transition={{ duration: 1 }}
                    whileHover={{ scale: 1.005 }}
                    className="hover:bg-blue-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    <td className="px-4 py-3 text-gray-700 dark:text-gray-200">{serial}</td>
                    <td className="px-4 py-3">
                      {row.website_link ? (
                        <a
                          href={row.website_link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-[#0071CE] dark:text-blue-400 hover:underline transition-colors"
                        >
                          Visit
                        </a>
                      ) : (
                        <span className="text-gray-400 dark:text-gray-500">-</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-gray-700 dark:text-gray-200">{row.category || "-"}</td>
                    <td className="px-4 py-3 text-gray-700 dark:text-gray-200 max-w-xs truncate">{row.product_name || "-"}</td>
                    <td className="px-4 py-3 text-gray-700 dark:text-gray-200">{row.product_type || "-"}</td>
                    <td className="px-4 py-3 text-gray-700 dark:text-gray-200">{row.item_id || "-"}</td>
                    {/* ✅ New Product Id column */}
                    <td className="px-4 py-3 text-gray-700 dark:text-gray-200">{row.product_id || "-"}</td>
                    <td className="px-4 py-3">
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap ${row.status === "Completed"
                            ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                            : row.status === "In Progress"
                              ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300"
                              : "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300"
                          }`}
                      >
                        {row.status || "Pending"}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <Button
                        size="sm"
                        disabled={researchingId === row.id_str || row.status === "Completed"}
                        className={`font-semibold rounded-full px-4 transition-colors ${row.status === "Completed"
                            ? "bg-gray-300 text-gray-600 cursor-not-allowed dark:bg-gray-700 dark:text-gray-400"
                            : "bg-[#FFC220] hover:bg-[#e6ad00] dark:bg-yellow-500 dark:hover:bg-yellow-600 text-black dark:text-gray-900"
                          }`}
                        onClick={() => handleResearch(row)}
                      >
                        {researchingId === row.id_str ? (
                          <>
                            <Loader className="animate-spin mr-1 h-3 w-3" />
                            Loading...
                          </>
                        ) : (
                          "Research"
                        )}
                      </Button>
                    </td>
                  </motion.tr>
                );
              })
            )}
          </tbody>

        </table>
      </div>

      {/* Pagination Controls - Enhanced dark mode */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6">
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          <span className="font-medium">{totalCount}</span> rows total
        </p>
        <div className="flex gap-2 items-center flex-wrap">
          <Button
            size="sm"
            variant="outline"
            className="border-[#0071CE] text-[#0071CE] hover:bg-[#0071CE] hover:text-white dark:border-blue-500 dark:text-blue-400 dark:hover:bg-blue-600 dark:hover:text-white rounded-full transition-colors"
            onClick={() => setPage(1)}
            disabled={page <= 1}
          >
            First
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="border-[#0071CE] text-[#0071CE] hover:bg-[#0071CE] hover:text-white dark:border-blue-500 dark:text-blue-400 dark:hover:bg-blue-600 dark:hover:text-white rounded-full transition-colors"
            onClick={() => setPage(page - 1)}
            disabled={page <= 1}
          >
            Prev
          </Button>
          <span className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-gray-700 dark:text-gray-200 text-sm font-medium">
            Page {page} of {totalPages || 1}
          </span>
          <Button
            size="sm"
            variant="outline"
            className="border-[#0071CE] text-[#0071CE] hover:bg-[#0071CE] hover:text-white dark:border-blue-500 dark:text-blue-400 dark:hover:bg-blue-600 dark:hover:text-white rounded-full transition-colors"
            onClick={() => setPage(page + 1)}
            disabled={page >= totalPages}
          >
            Next
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="border-[#0071CE] text-[#0071CE] hover:bg-[#0071CE] hover:text-white dark:border-blue-500 dark:text-blue-400 dark:hover:bg-blue-600 dark:hover:text-white rounded-full transition-colors"
            onClick={() => setPage(totalPages)}
            disabled={page >= totalPages}
          >
            Last
          </Button>
          <select
            value={pageSize}
            onChange={(e) => {
              setPageSize(Number(e.target.value));
              setPage(1);
            }}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-2 py-1 text-sm shadow-sm focus:ring-2 focus:ring-[#0071CE] dark:focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 transition-colors"
          >
            {[10, 25, 50, 100].map((size) => (
              <option key={size} value={size}>
                {size} / page
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
}

