export function getSession() {
  const raw = localStorage.getItem("session");
  if (!raw) return null;
  try {
    const { role, userId, expireAt } = JSON.parse(raw);
    if (typeof expireAt !== "number" || Date.now() > expireAt) {
      localStorage.removeItem("session");
      return null;
    }
    return { role, userId };
  } catch {
    return null;
  }
}

export function clearSession() {
  localStorage.removeItem("session");
}
