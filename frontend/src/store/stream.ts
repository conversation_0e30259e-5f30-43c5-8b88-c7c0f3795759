export async function streamText(
  endpoint: string,
  formData: FormData,
  onToken: (token: string) => void,
  options?: { timeoutMs?: number; signal?: AbortSignal }
) {
  const controller = new AbortController();
  const signal = options?.signal || controller.signal;

  const res = await fetch(endpoint, {
    method: "POST",
    body: formData,
    signal,
  });

  if (!res.ok || !res.body) {
    throw new Error(`Stream error: ${res.status}`);
  }

  const reader = res.body.getReader();
  const decoder = new TextDecoder();
  let done = false;

  while (!done) {
    const { value, done: readerDone } = await reader.read();
    done = readerDone;
    if (value) {
      const text = decoder.decode(value, { stream: true });
      onToken(text);
    }
  }
}
