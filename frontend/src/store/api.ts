import axios, { AxiosError } from "axios";
import type { AxiosRequestConfig } from "axios";

export const API_BASE_URL = (import.meta.env.VITE_API_BASE_URL as string) || "";

if (!API_BASE_URL) {
  console.warn("VITE_API_BASE_URL not set in .env");
}

export const api = axios.create({
  baseURL: API_BASE_URL || undefined,
  timeout: 30_000, // 30s timeout
  headers: {
    Accept: "application/json",
  },
});

// Attach token if available (localStorage used for session here)
api.interceptors.request.use((config: any) => {
  try {
    const raw = localStorage.getItem("session");
    if (raw && config.headers) {
      const parsed = JSON.parse(raw);
      // if you later store an auth token, attach it here
      if (parsed.token) {
        (config.headers as Record<string, string>)["Authorization"] = `Bearer ${parsed.token}`;
      }
    }
  } catch (e) {
    // ignore parsing errors
  }
  return config;
});

// Basic response interceptor for 401 handling and uniform errors
api.interceptors.response.use(
  (res) => res,
  (err: AxiosError) => {
    if (err.response?.status === 401) {
      // expire session and reload to force login
      localStorage.removeItem("session");
      // optional: navigate to login page - we can't import router here safely
      console.warn("Unauthorized - session cleared");
    }
    return Promise.reject(err);
  }
);

// Small helper for safe requests with optional retries
export async function safeRequest<T = any>(config: AxiosRequestConfig, retries = 1): Promise<T> {
  try {
    const res = await api.request<T>(config);
    return res.data;
  } catch (err) {
    if (retries > 0) {
      // naive backoff
      await new Promise((r) => setTimeout(r, 500));
      return safeRequest(config, retries - 1);
    }
    throw err;
  }
}
