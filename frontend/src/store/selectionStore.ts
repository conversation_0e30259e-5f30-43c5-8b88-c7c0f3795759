import { create } from "zustand";

type SelectionStore = {
  selections: Record<string, string[]>;
  editableValues: Record<string, Record<number, string>>;
  validations: Record<string, {
    walmart: Record<number, string>;
    llm: Record<number, string>;
    walmartLatest: Record<number, string>; // ✅ FIXED: Proper type
  }>;
  comments: Record<string, {
    walmart: Record<number, string>;
    llm: Record<number, string>;
    walmartLatest: Record<number, string>; // ✅ FIXED: Proper type
  }>;
  finalVerdicts: Record<string, Record<number, string>>;
  finalValidations: Record<string, Record<number, string>>; // ✅ NEW: Final validation state
  finalComments: Record<string, Record<number, string>>; // ✅ NEW: Final comment state
  setSelections: (id: string, cells: string[]) => void;
  clearSelections: (id: string) => void;
  setEditableValues: (id: string, values: Record<number, string>) => void;
  setValidations: (id: string, type: 'walmart' | 'llm' | 'walmartLatest', values: Record<number, string>) => void; // ✅ FIXED: Added walmartLatest
  setComments: (id: string, type: 'walmart' | 'llm' | 'walmartLatest', values: Record<number, string>) => void; // ✅ FIXED: Added walmartLatest
  setFinalVerdicts: (id: string, values: Record<number, string>) => void;
  setFinalValidations: (id: string, values: Record<number, string>) => void; // ✅ NEW: Final validation setter
  setFinalComments: (id: string, values: Record<number, string>) => void; // ✅ NEW: Final comment setter
};

const STORAGE_KEY = "walmart_app_session_v1";

const loadInitial = () => {
  try {
    const raw = sessionStorage.getItem(STORAGE_KEY);
    if (!raw) return {
      selections: {},
      editableValues: {},
      validations: {},
      comments: {},
      finalVerdicts: {},
      finalValidations: {},
      finalComments: {}
    };
    const parsed = JSON.parse(raw);
    if (typeof parsed === "object" && parsed !== null) return {
      ...parsed,
      finalValidations: parsed.finalValidations || {},
      finalComments: parsed.finalComments || {}
    };
    return {
      selections: {},
      editableValues: {},
      validations: {},
      comments: {},
      finalVerdicts: {},
      finalValidations: {},
      finalComments: {}
    };
  } catch (e) {
    console.warn("Could not load state from sessionStorage", e);
    return {
      selections: {},
      editableValues: {},
      validations: {},
      comments: {},
      finalVerdicts: {},
      finalValidations: {},
      finalComments: {}
    };
  }
};

const persist = (state: any) => {
  try {
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(state));
  } catch (e) {
    console.warn("Could not persist state to sessionStorage", e);
  }
};

export const useSelectionStore = create<SelectionStore>((set) => {
  const initialState = loadInitial();
  
  return {
    selections: initialState.selections || {},
    editableValues: initialState.editableValues || {},
    validations: initialState.validations || {},
    comments: initialState.comments || {},
    finalVerdicts: initialState.finalVerdicts || {},
    finalValidations: initialState.finalValidations || {},
    finalComments: initialState.finalComments || {},
    
    setSelections: (id, cells) =>
      set((state) => {
        const newState = { 
          ...state, 
          selections: { ...state.selections, [id]: cells }
        };
        persist(newState);
        return newState;
      }),
      
    clearSelections: (id) =>
      set((state) => {
        const newSelections = { ...state.selections };
        delete newSelections[id];
        const newState = { ...state, selections: newSelections };
        persist(newState);
        return newState;
      }),
      
    setEditableValues: (id, values) =>
      set((state) => {
        const newState = {
          ...state,
          editableValues: { ...state.editableValues, [id]: values }
        };
        persist(newState);
        return newState;
      }),
      
    // ✅ FIXED: Support walmartLatest type
    setValidations: (id, type, values) =>
      set((state) => {
        const newState = {
          ...state,
          validations: {
            ...state.validations,
            [id]: {
              ...state.validations[id],
              [type]: values
            }
          }
        };
        persist(newState);
        return newState;
      }),
      
    // ✅ FIXED: Support walmartLatest type
    setComments: (id, type, values) =>
      set((state) => {
        const newState = {
          ...state,
          comments: {
            ...state.comments,
            [id]: {
              ...state.comments[id],
              [type]: values
            }
          }
        };
        persist(newState);
        return newState;
      }),

    setFinalVerdicts: (id, values) =>
      set((state) => {
        const newState = {
          ...state,
          finalVerdicts: { ...state.finalVerdicts, [id]: values }
        };
        persist(newState);
        return newState;
      }),

    setFinalValidations: (id, values) =>
      set((state) => {
        const newState = {
          ...state,
          finalValidations: { ...state.finalValidations, [id]: values }
        };
        persist(newState);
        return newState;
      }),

    setFinalComments: (id, values) =>
      set((state) => {
        const newState = {
          ...state,
          finalComments: { ...state.finalComments, [id]: values }
        };
        persist(newState);
        return newState;
      }),
  };
});
