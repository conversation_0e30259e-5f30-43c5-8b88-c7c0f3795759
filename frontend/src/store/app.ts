import { create } from "zustand";
import { v4 as uuidv4 } from "uuid";
import type { ChatMessage, Role, User } from "../types"; // ✅ Import types

// Local definition because ../types does not export EnhancedUploadResponse
export interface EnhancedUploadResponse {
  preview?: Record<string, any>[];
  full_data?: Record<string, any>[];
  columns?: string[];
  original_columns?: string[];
}

export interface TaskWithAssignment {
  id: string;
  name: string;
  url?: string;
  assignedTo?: string | null;
  status: "pending" | "in-progress" | "completed";
}

interface AppState {
  uploaded?: EnhancedUploadResponse; // ✅ Use enhanced interface
  tasks: TaskWithAssignment[];
  selectedTaskId?: string;
  users: User[];
  role: Role | null;
  userId: string | null;
  selectedUrl: string | null;

  model: string;
  useGrounding: boolean;
  prompt: string;
  chat: ChatMessage[];
  streaming: boolean;

  setUploaded: (u: EnhancedUploadResponse) => void; // ✅ Updated type
  setTasks: (tasks: Partial<TaskWithAssignment>[]) => void;
  assignTask: (taskId: string, userId: string) => void;
  setSelectedTaskId: (id: string) => void;
  setUsers: (users: User[]) => void;
  addUser: (user: User) => void;
  setRole: (role: Role | null, userId: string | null) => void;
  setTaskStatus: (
    taskId: string,
    status: "pending" | "in-progress" | "completed"
  ) => void;

  setModel: (m: string) => void;
  setGrounding: (g: boolean) => void;
  setPrompt: (p: string) => void;
  addMessage: (m: ChatMessage) => void;
  updateLastMessage: (c: string) => void;
  setStreaming: (s: boolean) => void;
  getUserTasks: () => TaskWithAssignment[];
  setSelectedUrl: (url: string | null) => void;
  logout: () => void;
  
  // ✅ NEW: Helper methods for data management
  getFullDataForProcessing: () => any[] | null;
  getPreviewDataForStorage: () => any[] | null;
  hasFullData: () => boolean;
}

const ONE_HOUR_MS = 60 * 60 * 1000;

export const useAppStore = create<AppState>((set, get) => ({
  tasks: [],
  users: [
    { id: "1", username: "alice", email: "<EMAIL>", role: "user" },
    { id: "2", username: "bob", email: "<EMAIL>", role: "user" },
    { id: "3", username: "charlie", email: "<EMAIL>", role: "admin" },
  ],
  role: null,
  userId: null,
  selectedTaskId: undefined,
  model: localStorage.getItem("model") || "gemini-1.5-flash",
  useGrounding: localStorage.getItem("grounding") === "true",
  prompt: "",
  chat: [],
  streaming: false,
  selectedUrl: null,

  setSelectedUrl: (url) => set({ selectedUrl: url }),
  setRole: (role, userId) => {
    if (role && userId) {
      const expireAt = Date.now() + ONE_HOUR_MS;
      localStorage.setItem("session", JSON.stringify({ role, userId, expireAt }));
      set({ role, userId });
    } else {
      set({ role: null, userId: null });
      localStorage.removeItem("session");
    }
  },
  logout: () => {
    localStorage.removeItem("session");
    set({
      role: null,
      userId: null,
      selectedTaskId: undefined,
      selectedUrl: null,
      chat: [],
      streaming: false,
      model: localStorage.getItem("model") || "gemini-1.5-flash",
      useGrounding: localStorage.getItem("grounding") === "true",
      prompt: "",
      uploaded: undefined, // ✅ Clear uploaded data on logout
    });
  },

  setUsers: (users) => set({ users }),
  addUser: (user) => set((s) => ({ users: [...s.users, user] })),
  
  // ✅ ENHANCED: Updated setUploaded with data validation
  setUploaded: (u) => {
    console.log("📊 Setting uploaded data:", {
      hasPreview: !!u.preview,
      previewLength: u.preview?.length || 0,
      hasFullData: !!u.full_data,
      fullDataLength: u.full_data?.length || 0,
      previewColumns: u.columns?.length || 0,
      originalColumns: u.original_columns?.length || 0,
    });
    
    set({ uploaded: u });
  },
  
  setTasks: (tasks) =>
    set({
      tasks: tasks.map((task) => {
        const status = String(task.status || "pending");
        const name = task.name || (task as any).title || "Unnamed Task";
        return {
          id: task.id || uuidv4(),
          name,
          url: task.url,
          assignedTo: task.assignedTo ?? null,
          status:
            status === "done" || status === "complete"
              ? "completed"
              : (status as "pending" | "in-progress" | "completed"),
        };
      }),
    }),
  assignTask: (taskId, userId) =>
    set((s) => ({
      tasks: s.tasks.map((tsk) =>
        tsk.id === taskId ? { ...tsk, assignedTo: userId } : tsk
      ),
    })),
  setTaskStatus: (taskId, status) =>
    set((s) => ({
      tasks: s.tasks.map((tsk) =>
        tsk.id === taskId ? { ...tsk, status } : tsk
      ),
    })),
  setSelectedTaskId: (id) => set({ selectedTaskId: id }),
  setModel: (m) => {
    localStorage.setItem("model", m);
    set({ model: m });
  },
  setGrounding: (g) => {
    localStorage.setItem("grounding", g.toString());
    set({ useGrounding: g });
  },
  setPrompt: (p) => set({ prompt: p }),
  addMessage: (m) => set((s) => ({ chat: [...s.chat, m] })),
  updateLastMessage: (c) =>
    set((s) => {
      const chat = [...s.chat];
      if (chat.length > 0) chat[chat.length - 1].content = c;
      return { chat };
    }),
  setStreaming: (s) => set({ streaming: s }),
  getUserTasks: () => {
    const { tasks, userId } = get();
    return tasks.filter((t) => t.assignedTo === userId);
  },

  // ✅ NEW: Helper methods for data management
  getFullDataForProcessing: () => {
    const { uploaded } = get();
    if (!uploaded) return null;
    
    // Prefer full_data for processing (140+ columns), fallback to preview
    const data = uploaded.full_data || uploaded.preview;
    console.log("📊 Getting data for processing:", {
      source: uploaded.full_data ? "full_data" : "preview",
      rowCount: data?.length || 0,
      sampleColumns: data?.[0] ? Object.keys(data[0]).slice(0, 5) : [],
      totalColumns: data?.[0] ? Object.keys(data[0]).length : 0
    });
    
    return data || null;
  },

  getPreviewDataForStorage: () => {
    const { uploaded } = get();
    if (!uploaded) return null;
    
    // Always use preview for BigQuery storage (24 columns)
    console.log("💾 Getting data for BigQuery storage:", {
      rowCount: uploaded.preview?.length || 0,
      columns: uploaded.columns?.length || 0
    });
    
    return uploaded.preview || null;
  },

  hasFullData: () => {
    const { uploaded } = get();
    return !!(uploaded?.full_data && uploaded.full_data.length > 0);
  }
}));
