{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-toast": "^1.2.15", "@tanstack/react-table": "^8.21.3", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "framer-motion": "^12.23.12", "lodash": "^4.17.21", "lucide-react": "^0.540.0", "marked": "^16.3.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-markdown": "^10.1.0", "react-router-dom": "^7.8.1", "react-textarea-autosize": "^8.5.9", "recharts": "^3.2.0", "remark-gfm": "^4.0.1", "tailwind-variants": "^2.1.0", "uuid": "^11.1.0", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/lodash": "^4.17.20", "@types/node": "^24.3.0", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.8", "@types/react-dropzone": "^4.2.2", "@types/react-textarea-autosize": "^4.3.6", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}